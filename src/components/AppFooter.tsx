import React from "react";
import Image from "next/image";
import Link from "next/link";
import { useIntl } from "react-intl";
import { ROUTE_PATHS } from "@/constants/app";

const AppFooter = () => {
  const intl = useIntl();

  const solutionsLinks = [
    {
      name: "companyRegistration",
      href: ROUTE_PATHS.companyRegistration,
    },
    {
      name: "secretary",
      href: ROUTE_PATHS.secretary,
    },
    {
      name: "bookkeeping",
      href: ROUTE_PATHS.bookkeeping,
    },
    {
      name: "accountingAudit",
      href: ROUTE_PATHS.accountingAudit,
    },
  ];

  const getStartedLinks = [
    {
      name: "pricing",
      href: ROUTE_PATHS.pricing,
    },
  ];

  const companyLinks = [
    {
      name: "aboutUs",
      href: ROUTE_PATHS.aboutUs,
    },
  ];

  return (
    <footer className="bg-dark-100 text-white">
      <div className="custom-container pt-6 lg:pt-14 h-140 md:py-16">
        <div className="flex flex-col gap-8 lg:flex-row lg:justify-between">
          {/* Left Section */}
          <div className="space-y-6">
            <Link href="/" className="inline-block">
              <Image
                src="/logo-white.png"
                width={240}
                height={48}
                alt="comsecbase logo"
                className="h-8 md:h-12 w-auto"
              />
            </Link>
            <div className="max-w-md">
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold leading-tight">
                {intl.formatMessage({ id: "footer.tagline" })}
              </h2>
            </div>
          </div>

          {/* Right Section - Navigation Links */}
          <div className="flex flex-col md:flex-row gap-8 md:gap-32">
            {/* Solutions for Business */}
            <div>
              <h3 className="text-sm font-semibold text-white opacity-40 uppercase tracking-wider mb-4">
                {intl.formatMessage({ id: "footer.solutionsForBusiness" })}
              </h3>
              <ul className="space-y-3">
                {solutionsLinks.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-white hover:text-gray-300 transition-colors duration-200"
                    >
                      {intl.formatMessage({
                        id: `footer.${link.name}`,
                      })}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Get Started & Company */}
            <div className="space-y-8">
              {/* Get Started */}
              <div>
                <h3 className="text-sm font-semibold text-white opacity-40 uppercase tracking-wider mb-4">
                  {intl.formatMessage({ id: "footer.getStarted" })}
                </h3>
                <ul className="space-y-3">
                  {getStartedLinks.map((link) => (
                    <li key={link.name}>
                      <Link
                        href={link.href}
                        className="text-white hover:text-gray-300 transition-colors duration-200"
                      >
                        {intl.formatMessage({
                          id: `common.navigation.${link.name}`,
                        })}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Company */}
              <div>
                <h3 className="text-sm font-semibold text-white opacity-40 uppercase tracking-wider mb-4">
                  {intl.formatMessage({ id: "footer.company" })}
                </h3>
                <ul className="space-y-3">
                  {companyLinks.map((link) => (
                    <li key={link.name}>
                      <Link
                        href={link.href}
                        className="text-white hover:text-gray-300 transition-colors duration-200"
                      >
                        {intl.formatMessage({
                          id: `common.navigation.${link.name}`,
                        })}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default AppFooter;
