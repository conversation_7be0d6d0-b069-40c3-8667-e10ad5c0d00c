/* eslint-disable @typescript-eslint/no-explicit-any */

import "@/styles/globals.css";
import type { AppProps } from "next/app";
import { Inter } from "next/font/google";
import { useRouter } from "next/router";
import { IntlProvider } from "react-intl";
import { getLocaleMessages } from "@/utils/locale";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

const getLocale = (language: any) => {
  return {
    locale: language,
    messages: getLocaleMessages(language),
  };
};

export default function App({ Component, pageProps }: AppProps) {
  const router = useRouter();
  const { locale, defaultLocale } = router;
  const { locale: userLocale, messages } = getLocale(locale);
  return (
    <IntlProvider
      locale={userLocale}
      defaultLocale={defaultLocale}
      messages={messages}
    >
      <div className={`${inter.variable} ${inter.className}`}>
        <Component {...pageProps} />
      </div>
    </IntlProvider>
  );
}
