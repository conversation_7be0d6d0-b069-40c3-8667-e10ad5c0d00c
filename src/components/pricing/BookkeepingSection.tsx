import React from "react";
import { Button } from "@/components/ui/button";
import { Check } from "lucide-react";

interface PricingTier {
  transactions: string;
  fee: string;
}

export default function BookkeepingSection() {
  const services = [
    "Annual bookkeeping",
    "Annual Unaudited Financial Statements",
    "Annual management reports",
  ];

  const optionalServices = ["Employer Return filing, per employee"];

  const pricingTiers: PricingTier[] = [
    { transactions: "Below 30", fee: "800" },
    { transactions: "31 - 100", fee: "1,200" },
    { transactions: "101 - 240", fee: "1,800" },
    { transactions: "241 - 360", fee: "2,400" },
    { transactions: "361 - 480", fee: "3,000" },
    { transactions: "481 - 600", fee: "3,800" },
    { transactions: "601 - 800", fee: "4,600" },
    { transactions: "801 - 1000", fee: "5,500" },
    { transactions: "1001 - 1200", fee: "7,000" },
    { transactions: "1201 - 1400", fee: "8,500" },
    { transactions: "1401 - 1600", fee: "10,000" },
    { transactions: "1601 - 1800", fee: "11,500" },
    { transactions: "1801 - 2000", fee: "13,000" },
    { transactions: "2001 - 2250", fee: "15,000" },
    { transactions: "2251 - 2500", fee: "17,000" },
    { transactions: "2401 - 2600", fee: "17,500" },
    { transactions: "2601 - 3000", fee: "20,000" },
    { transactions: "Above 3000", fee: "Independent quotation" },
  ];

  return (
    <div className="w-full max-w-7xl mx-auto p-4 md:p-6 ">
      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        {/* Header Section */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 p-4 md:p-6 lg:p-8 bg-white border-b">
          {/* Left Column - Service Info */}
          <div className="lg:col-span-2 space-y-4 md:space-y-6">
            <div className="space-y-2 md:space-y-4">
              <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900">
                Bookkeeping Service
              </h1>
              <p className="text-sm md:text-base text-gray-600">
                The service charges are for reference only; actual fees will
                depend on individual circumstances.
              </p>
            </div>

            {/* Services Included */}
            <div className="space-y-4">
              <h3 className="text-lg md:text-xl font-semibold text-gray-900">
                Services included
              </h3>
              <div className="space-y-3">
                {services.map((service, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <Check className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <span className="text-sm md:text-base text-gray-700">
                      {service}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Optional Add-ons */}
            <div className="space-y-4">
              <h3 className="text-lg md:text-xl font-semibold text-gray-900">
                Optional add-ons
              </h3>
              <div className="space-y-3">
                {optionalServices.map((service, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <span className="text-sm md:text-base text-gray-700">
                      {service}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Column - Basic Plan Card */}
          <div className="lg:col-span-1 flex justify-center lg:justify-end">
            <div className="w-full max-w-sm text-center">
              <div className="pb-4">
                <div className="text-xl md:text-2xl font-bold text-gray-900">
                  Basic Plan
                </div>
              </div>
              <div className="pt-0">
                <Button className="w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-6 rounded-xl transition-colors duration-200">
                  Get this plan
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Pricing Table */}
        <div className="p-4 md:p-6 lg:p-8">
          <div className="overflow-x-auto">
            <div className="min-w-full">
              {/* Table Header */}
              <div className="grid grid-cols-2 gap-4 mb-6 pb-4 border-b-2 border-gray-200">
                <div className="text-center">
                  <h3 className="text-base md:text-lg font-semibold text-gray-900">
                    Transactions (per year)
                  </h3>
                </div>
                <div className="text-center">
                  <h3 className="text-base md:text-lg font-semibold text-gray-900">
                    Fee(HKD)
                  </h3>
                </div>
              </div>

              {/* Table Rows */}
              <div className="space-y-2 md:space-y-3">
                {pricingTiers.map((tier, index) => (
                  <div
                    key={index}
                    className="grid grid-cols-2 gap-4 py-2 md:py-3 border-b border-gray-100 last:border-b-0 hover: transition-colors rounded-md"
                  >
                    <div className="text-center">
                      <span className="text-sm md:text-base text-gray-700">
                        {tier.transactions}
                      </span>
                    </div>
                    <div className="text-center">
                      <span className="text-sm md:text-base font-medium text-gray-900">
                        {tier.fee === "Independent quotation"
                          ? tier.fee
                          : `${tier.fee}`}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
