import React from "react";
import GlobalContainer from "@/components/GlobalContainer";
import FeatureServicesSection from "@/components/companyRegistration/FeatureServicesSection";
import EvaluateSection from "@/components/companyRegistration/EvaluateSection";
import ServicesProvidedSection from "@/components/companyRegistration/ServicesProvidedSection";
import HelpSection from "@/components/companyRegistration/HelpSection";
import ChooseUsSection from "@/components/companyRegistration/ChooseUsSection";
import LaunchSection from "@/components/companyRegistration/ScheduleACallSection";
import StepsSection from "@/components/companyRegistration/StepsSection";
import Pricing from "@/components/companyRegistration/Pricing";
import FAQs from "@/components/companyRegistration/FAQs";
import PartnerNetworkSection from "@/components/companyRegistration/PartnerNetworkSection";

export default function Features() {
  return (
    <GlobalContainer headerDark={true}>
      <FeatureServicesSection />
      <EvaluateSection />
      <ServicesProvidedSection />
      <HelpSection />
      <ChooseUsSection />
      <LaunchSection />
      <StepsSection />
      <Pricing />
      <PartnerNetworkSection />
      <FAQs />
    </GlobalContainer>
  );
}
