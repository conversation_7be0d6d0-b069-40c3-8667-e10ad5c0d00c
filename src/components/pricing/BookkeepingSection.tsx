import React from "react";
import { useIntl } from "react-intl";
import { Button } from "@/components/ui/button";
import { Check } from "lucide-react";

interface PricingTier {
  transactions: string;
  fee: string;
}

export default function BookkeepingSection() {
  const intl = useIntl();

  const services = [
    intl.formatMessage({ id: "pricing.bookkeeping.service1" }),
    intl.formatMessage({ id: "pricing.bookkeeping.service2" }),
    intl.formatMessage({ id: "pricing.bookkeeping.service3" }),
  ];

  const optionalServices = [
    intl.formatMessage({ id: "pricing.bookkeeping.optional1" }),
  ];

  const pricingTiers: PricingTier[] = [
    {
      transactions: intl.formatMessage({ id: "pricing.bookkeeping.below30" }),
      fee: "800",
    },
    {
      transactions: intl.formatMessage({
        id: "pricing.bookkeeping.range31to100",
      }),
      fee: "1,200",
    },
    {
      transactions: intl.formatMessage({
        id: "pricing.bookkeeping.range101to240",
      }),
      fee: "1,800",
    },
    {
      transactions: intl.formatMessage({
        id: "pricing.bookkeeping.range241to360",
      }),
      fee: "2,400",
    },
    {
      transactions: intl.formatMessage({
        id: "pricing.bookkeeping.range361to480",
      }),
      fee: "3,000",
    },
    {
      transactions: intl.formatMessage({
        id: "pricing.bookkeeping.range481to600",
      }),
      fee: "3,800",
    },
    {
      transactions: intl.formatMessage({
        id: "pricing.bookkeeping.range601to800",
      }),
      fee: "4,600",
    },
    {
      transactions: intl.formatMessage({
        id: "pricing.bookkeeping.range801to1000",
      }),
      fee: "5,500",
    },
    {
      transactions: intl.formatMessage({
        id: "pricing.bookkeeping.range1001to1200",
      }),
      fee: "7,000",
    },
    {
      transactions: intl.formatMessage({
        id: "pricing.bookkeeping.range1201to1400",
      }),
      fee: "8,500",
    },
    {
      transactions: intl.formatMessage({
        id: "pricing.bookkeeping.range1401to1600",
      }),
      fee: "10,000",
    },
    {
      transactions: intl.formatMessage({
        id: "pricing.bookkeeping.range1601to1800",
      }),
      fee: "11,500",
    },
    {
      transactions: intl.formatMessage({
        id: "pricing.bookkeeping.range1801to2000",
      }),
      fee: "13,000",
    },
    {
      transactions: intl.formatMessage({
        id: "pricing.bookkeeping.range2001to2250",
      }),
      fee: "15,000",
    },
    {
      transactions: intl.formatMessage({
        id: "pricing.bookkeeping.range2251to2500",
      }),
      fee: "17,000",
    },
    {
      transactions: intl.formatMessage({
        id: "pricing.bookkeeping.range2401to2600",
      }),
      fee: "17,500",
    },
    {
      transactions: intl.formatMessage({
        id: "pricing.bookkeeping.range2601to3000",
      }),
      fee: "20,000",
    },
    {
      transactions: intl.formatMessage({ id: "pricing.bookkeeping.above3000" }),
      fee: intl.formatMessage({
        id: "pricing.bookkeeping.independentQuotation",
      }),
    },
  ];

  return (
    <div className="w-full max-w-7xl mx-auto p-4 md:p-6 ">
      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        {/* Header Section */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 p-4 md:p-6 lg:p-8 bg-white border-b">
          {/* Left Column - Service Info */}
          <div className="lg:col-span-2 space-y-4 md:space-y-6">
            <div className="space-y-2 md:space-y-4">
              <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900">
                {intl.formatMessage({ id: "pricing.bookkeeping.title" })}
              </h1>
              <p className="text-sm md:text-base text-gray-600">
                {intl.formatMessage({ id: "pricing.bookkeeping.subtitle" })}
              </p>
            </div>

            {/* Services Included */}
            <div className="space-y-4">
              <h3 className="text-lg md:text-xl font-semibold text-gray-900">
                {intl.formatMessage({
                  id: "pricing.bookkeeping.servicesIncluded",
                })}
              </h3>
              <div className="space-y-3">
                {services.map((service, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <Check className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <span className="text-sm md:text-base text-gray-700">
                      {service}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Optional Add-ons */}
            <div className="space-y-4">
              <h3 className="text-lg md:text-xl font-semibold text-gray-900">
                {intl.formatMessage({
                  id: "pricing.bookkeeping.optionalAddons",
                })}
              </h3>
              <div className="space-y-3">
                {optionalServices.map((service, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <span className="text-sm md:text-base text-gray-700">
                      {service}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Column - Basic Plan Card */}
          <div className="lg:col-span-1 flex justify-center lg:justify-end">
            <div className="w-full max-w-sm text-center">
              <div className="pb-4">
                <div className="text-xl md:text-2xl font-bold text-gray-900">
                  {intl.formatMessage({ id: "pricing.bookkeeping.basicPlan" })}
                </div>
              </div>
              <div className="pt-0">
                <Button className="w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-6 rounded-xl transition-colors duration-200">
                  {intl.formatMessage({ id: "pricing.bookkeeping.buttonText" })}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Pricing Table */}
        <div className="p-4 md:p-6 lg:p-8">
          <div className="overflow-x-auto">
            <div className="min-w-full">
              {/* Table Header */}
              <div className="grid grid-cols-2 gap-4 mb-6 pb-4 border-b-2 border-gray-200">
                <div className="text-center">
                  <h3 className="text-base md:text-lg font-semibold text-gray-900">
                    {intl.formatMessage({
                      id: "pricing.bookkeeping.transactionsHeader",
                    })}
                  </h3>
                </div>
                <div className="text-center">
                  <h3 className="text-base md:text-lg font-semibold text-gray-900">
                    {intl.formatMessage({
                      id: "pricing.bookkeeping.feeHeader",
                    })}
                  </h3>
                </div>
              </div>

              {/* Table Rows */}
              <div className="space-y-2 md:space-y-3">
                {pricingTiers.map((tier, index) => (
                  <div
                    key={index}
                    className="grid grid-cols-2 gap-4 py-2 md:py-3 border-b border-gray-100 last:border-b-0 hover: transition-colors rounded-md"
                  >
                    <div className="text-center">
                      <span className="text-sm md:text-base text-gray-700">
                        {tier.transactions}
                      </span>
                    </div>
                    <div className="text-center">
                      <span className="text-sm md:text-base font-medium text-gray-900">
                        {tier.fee ===
                        intl.formatMessage({
                          id: "pricing.bookkeeping.independentQuotation",
                        })
                          ? tier.fee
                          : `${tier.fee}`}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
