import React from "react";
import Image from "next/image";
import { useIntl } from "react-intl";
import BadgeHeader from "../BadgeHeader";

interface HelpCardProps {
  title: string;
  description: string;
  imageSrc: string;
  imageAlt: string;
}

const HelpCard: React.FC<HelpCardProps> = ({
  title,
  description,
  imageSrc,
  imageAlt,
}) => {
  return (
    <div className="relative rounded-2xl overflow-hidden h-80 group cursor-pointer">
      <div className="absolute inset-0">
        <Image
          src={imageSrc}
          alt={imageAlt}
          fill
          className="object-cover transition-transform duration-300 group-hover:scale-105"
        />
      </div>
      <div className="absolute inset-0 bg-gradient-to-b from-black/20 via-black/40 to-black/70"></div>
      <div className="absolute z-10 md:left-8 md:bottom-8 left-4 bottom-4 text-white md:max-w-2/3 mb-4 md:mb-0">
        <div className="text-2xl font-[600]">{title}</div>
        <div className="font-normal mt-2">{description}</div>
      </div>
    </div>
  );
};

export default function HelpSection() {
  const intl = useIntl();

  const helpCategories = [
    {
      title: intl.formatMessage({ id: "home.help.startups.title" }),
      description: intl.formatMessage({ id: "home.help.startups.description" }),
      imageSrc: "/imgs/startUps.jpg",
      imageAlt: "Start-ups",
    },
    {
      title: intl.formatMessage({ id: "home.help.established.title" }),
      description: intl.formatMessage({
        id: "home.help.established.description",
      }),
      imageSrc: "/imgs/establishedCompanies.jpg",
      imageAlt: "Established Companies",
    },
    {
      title: intl.formatMessage({ id: "home.help.local.title" }),
      description: intl.formatMessage({ id: "home.help.local.description" }),
      imageSrc: "/imgs/localCompanies.jpg",
      imageAlt: "Local Companies",
    },
    {
      title: intl.formatMessage({ id: "home.help.foreign.title" }),
      description: intl.formatMessage({ id: "home.help.foreign.description" }),
      imageSrc: "/imgs/foreignEntrepreneurs.jpg",
      imageAlt: "Foreign Entrepreneurs",
    },
  ];

  return (
    <section className="custom-container py-12 md:py-20 lg:py-24 xl:py-28">
      <div>
        <BadgeHeader
          badgeText={intl.formatMessage({ id: "home.help.badge" })}
          badgeBgColor="bg-golden-100"
          badgeTextColor="text-golden-200"
          title={intl.formatMessage({ id: "home.help.title" })}
        />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8">
          {helpCategories.map((category, index) => (
            <HelpCard
              key={index}
              title={category.title}
              description={category.description}
              imageSrc={category.imageSrc}
              imageAlt={category.imageAlt}
            />
          ))}
        </div>
      </div>
    </section>
  );
}
