import React from "react";
import GlobalContainer from "@/components/GlobalContainer";
import FeatureServicesSection from "@/components/accountingAudit/FeatureServicesSection";
import EvaluateSection from "@/components/accountingAudit/EvaluateSection";
import ServicesProvidedSection from "@/components/accountingAudit/ServicesProvidedSection";
import HelpSection from "@/components/accountingAudit/HelpSection";
import ChooseUsSection from "@/components/accountingAudit/ChooseUsSection";
import LaunchSection from "@/components/accountingAudit/ScheduleACallSection";
import Pricing from "@/components/accountingAudit/Pricing";
import FAQs from "@/components/accountingAudit/FAQs";
import PartnerNetworkSection from "@/components/accountingAudit/PartnerNetworkSection";

export default function Features() {
  return (
    <GlobalContainer headerDark={true}>
      <FeatureServicesSection />
      <EvaluateSection />
      <ServicesProvidedSection />
      <HelpSection />
      <ChooseUsSection />
      <LaunchSection />
      <Pricing />
      <PartnerNetworkSection />
      <FAQs />
    </GlobalContainer>
  );
}
