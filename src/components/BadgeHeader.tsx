import React from "react";
import { cn } from "@/lib/utils";

const BadgeHeader = ({
  badgeBgColor,
  badgeTextColor,
  badgeText,
  title,
  titleHighlight,
  titleCls,
  subTitle,
  subTitleCls,
}: {
  badgeBgColor: string;
  badgeTextColor: string;
  badgeText: string;
  title: string;
  titleHighlight?: string;
  titleCls?: string;
  subTitle?: string;
  subTitleCls?: string;
}) => {
  return (
    <div className="mb-6">
      <div className="flex flex-col justify-center items-center">
        <div
          className={cn(
            "rounded-lg lg:p-2 px-2 py-1 font-extrabold lg:mb-6 mb-2 lg:text-base text-sm",
            badgeBgColor,
            badgeTextColor
          )}
        >
          {badgeText}
        </div>
        <div
          className={cn(
            "font-[900] lg:text-5xl text-4xl mb-4 text-center",
            titleCls
          )}
        >
          {title} {titleHighlight}
        </div>
        {subTitle && (
          <div
            className={cn("text-center text-base mb-4 lg:w-3/5", subTitleCls)}
          >
            {subTitle}
          </div>
        )}
      </div>
    </div>
  );
};

export default BadgeHeader;
