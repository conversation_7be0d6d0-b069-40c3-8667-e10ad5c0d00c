import React from "react";
import { Button } from "@/components/ui/button";
import { Check, X } from "lucide-react";

interface ServiceItem {
  name: string;
  greenBox: boolean | string;
  essential: boolean | string;
  comprehensive: boolean | string;
}

interface PricingPlan {
  name: string;
  price: string;
  buttonText: string;
  buttonVariant: "default" | "secondary" | "outline";
}

export default function CompanyRegistrationSection() {
  const plans: PricingPlan[] = [
    {
      name: "Green Box Package",
      price: "HK$4,680",
      buttonText: "Get this plan",
      buttonVariant: "default",
    },
    {
      name: "Essential Package",
      price: "HK$6,180",
      buttonText: "Get this plan",
      buttonVariant: "default",
    },
    {
      name: "Comprehensive Package",
      price: "HK$7,680",
      buttonText: "Get this plan",
      buttonVariant: "default",
    },
  ];

  const includedServices: ServiceItem[] = [
    {
      name: "Company Registry Fee",
      greenBox: "$1,720",
      essential: "$1,720",
      comprehensive: "$1,720",
    },
    {
      name: "Company Registry Fee",
      greenBox: "$2,200",
      essential: "$2,200",
      comprehensive: "$2,200",
    },
  ];

  const incorporationServices: ServiceItem[] = [
    {
      name: "Company Name Search",
      greenBox: true,
      essential: true,
      comprehensive: true,
    },
    {
      name: "Business Registration Certificate (BR)",
      greenBox: true,
      essential: true,
      comprehensive: true,
    },
    {
      name: "Certificate of Incorporation (CI)",
      greenBox: true,
      essential: true,
      comprehensive: true,
    },
    {
      name: "Articles of Association (AA)",
      greenBox: "5 Copies",
      essential: "5 Copies",
      comprehensive: "5 Copies",
    },
    {
      name: "Preparation of Company Register",
      greenBox: true,
      essential: true,
      comprehensive: true,
    },
    {
      name: "Preparation of Register of Significant Controllers",
      greenBox: true,
      essential: true,
      comprehensive: true,
    },
    {
      name: "Preparation of First Board Meeting",
      greenBox: true,
      essential: true,
      comprehensive: true,
    },
    {
      name: "Company Share Register",
      greenBox: true,
      essential: true,
      comprehensive: true,
    },
    {
      name: "One Company Seal and One Signature Seal",
      greenBox: true,
      essential: true,
      comprehensive: true,
    },
    {
      name: "Required time: Approximately 3-5 working days",
      greenBox: true,
      essential: true,
      comprehensive: true,
    },
  ];

  const secretaryServices: ServiceItem[] = [
    {
      name: "Preparation of Annual General Meeting documents",
      greenBox: false,
      essential: true,
      comprehensive: true,
    },
    {
      name: "Annual Returns Filing (NAR1), including HK$ 105 government fee",
      greenBox: false,
      essential: true,
      comprehensive: true,
    },
  ];

  const registeredAddressServices: ServiceItem[] = [
    {
      name: "Notifications when you receive mail",
      greenBox: false,
      essential: false,
      comprehensive: true,
    },
    {
      name: "Scanning and forwarding of notices and documents to this address",
      greenBox: false,
      essential: false,
      comprehensive: true,
    },
  ];

  const optionalServices: ServiceItem[] = [
    {
      name: "Traditional bank account opening assistance",
      greenBox: "$4,800",
      essential: "$4,800",
      comprehensive: "$4,800",
    },
  ];

  const renderServiceValue = (value: boolean | string) => {
    if (typeof value === "boolean") {
      return value ? (
        <Check className="h-5 w-5 mx-auto" />
      ) : (
        <X className="h-5 w-5 mx-auto" />
      );
    }
    return <span className="text-sm">{value}</span>;
  };

  const ServiceSection = ({
    title,
    services,
    className = "",
  }: {
    title: string;
    services: ServiceItem[];
    className?: string;
  }) => (
    <div className={`space-y-4 ${className}`}>
      <h3 className="text-lg font-semibold pb-2">{title}</h3>
      {services.map((service, index) => (
        <div
          key={index}
          className="grid grid-cols-1 md:grid-cols-4 gap-4 py-3 border-b border-[#D9D9D9]"
        >
          <div className="md:col-span-1">
            <p className="text-sm font-medium">{service.name}</p>
          </div>
          <div className="grid grid-cols-3 md:contents gap-4">
            <div className="flex justify-center items-center">
              {renderServiceValue(service.greenBox)}
            </div>
            <div className="flex justify-center items-center">
              {renderServiceValue(service.essential)}
            </div>
            <div className="flex justify-center items-center">
              {renderServiceValue(service.comprehensive)}
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <div className="w-full max-w-7xl mx-auto p-6 ">
      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        {/* Header Section */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 p-6 bg-white">
          <div className="md:col-span-1">
            <div className="space-y-4">
              <h2 className="text-2xl font-bold">
                Company Registration Services
              </h2>
              <p className="text-sm ">
                For those who already have a Hong Kong office address
              </p>
            </div>
          </div>

          {/* Pricing Cards */}
          <div className="md:col-span-3 grid grid-cols-1 md:grid-cols-3 gap-4">
            {plans.map((plan, index) => (
              <div key={index} className="text-center border-0">
                <div className="pb-4">
                  <div className="text-2xl font-semibold">{plan.name}</div>
                  <div className="space-y-2">
                    <p className="text-sm font-medium ">Basic Price</p>
                    <p className="text-3xl font-bold">{plan.price}</p>
                  </div>
                </div>
                <div className="pt-0">
                  <Button
                    className="w-full bg-primary"
                    variant={plan.buttonVariant}
                  >
                    {plan.buttonText}
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Services Comparison */}
        <div className="p-6 space-y-8">
          {/* Mobile Headers for Service Columns */}
          <div className="md:hidden grid grid-cols-3 gap-4 mb-6 text-center">
            <div className="font-semibold text-sm">Green Box</div>
            <div className="font-semibold text-sm">Essential</div>
            <div className="font-semibold text-sm">Comprehensive</div>
          </div>

          <ServiceSection title="Included" services={includedServices} />
          <ServiceSection
            title="Incorporation"
            services={incorporationServices}
          />
          <ServiceSection
            title="Company secretary services"
            services={secretaryServices}
          />
          <ServiceSection
            title="Registered address"
            services={registeredAddressServices}
          />
          <ServiceSection title="Optional add-on" services={optionalServices} />
        </div>
      </div>
    </div>
  );
}
