import React from "react";
import Image from "next/image";
import { useIntl } from "react-intl";
import { Button } from "../ui/button";

const FooterBannerSection = () => {
  const intl = useIntl();

  return (
    <section className="custom-container md:my-28 my-14">
      <div className="relative rounded-2xl overflow-hidden h-64 md:h-80 lg:h-96">
        <div className="absolute inset-0">
          <Image
            src="/imgs/home-footer-banner.jpg"
            alt="Footer banner background"
            fill
            className="object-cover"
            priority
          />
        </div>

        <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/40 to-black/70"></div>

        {/* Content */}
        <div className="absolute inset-0 flex items-center lg:justify-end">
          <div className="text-white p-4 md:p-8 lg:p-12 max-w-md lg:max-w-lg">
            <div className="text-2xl md:text-3xl lg:text-4xl font-bold mb-2 md:mb-4">
              <h2>{intl.formatMessage({ id: "home.footerBanner.title" })}</h2>
              <h2>
                {intl.formatMessage({ id: "home.footerBanner.subtitle" })}
              </h2>
            </div>

            <p className="text-base md:text-lg mb-2 md:mb-6 opacity-90">
              {intl.formatMessage({ id: "home.footerBanner.description" })}
            </p>
            <Button className="bg-blue-200 text-white font-bold rounded-lg px-2 py-1 !h-8">
              {intl.formatMessage({ id: "home.footerBanner.button" })}
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FooterBannerSection;
