import React from "react";
import { useIntl } from "react-intl";
import Image from "next/image";
import BadgeHeader from "../BadgeHeader";

const ServicesProvidedSection = () => {
  const intl = useIntl();

  const ServicesData = [
    {
      icon: "/imgs/service1.png",
      title: intl.formatMessage({
        id: "companyRegistration.servicesProvided.title1",
      }),
      description: intl.formatMessage({
        id: "companyRegistration.servicesProvided.description1",
      }),
    },
    {
      icon: "/imgs/service2.png",
      title: intl.formatMessage({
        id: "companyRegistration.servicesProvided.title2",
      }),
      description: intl.formatMessage({
        id: "companyRegistration.servicesProvided.description2",
      }),
    },
    {
      icon: "/imgs/service3.png",
      title: intl.formatMessage({
        id: "companyRegistration.servicesProvided.title3",
      }),
      description: intl.formatMessage({
        id: "companyRegistration.servicesProvided.description3",
      }),
    },
    {
      icon: "/imgs/service4.png",
      title: intl.formatMessage({
        id: "companyRegistration.servicesProvided.title4",
      }),
      description: intl.formatMessage({
        id: "companyRegistration.servicesProvided.description4",
      }),
    },
    {
      icon: "/imgs/service5.png",
      title: intl.formatMessage({
        id: "companyRegistration.servicesProvided.title5",
      }),
      description: intl.formatMessage({
        id: "companyRegistration.servicesProvided.description5",
      }),
    },
    {
      icon: "/imgs/service6.png",
      title: intl.formatMessage({
        id: "companyRegistration.servicesProvided.title6",
      }),
      description: intl.formatMessage({
        id: "companyRegistration.servicesProvided.description6",
      }),
    },
  ];
  return (
    <section className="py-12 md:py-20 lg:py-24 xl:py-28">
      <div className="custom-container">
        <BadgeHeader
          badgeText={intl.formatMessage({
            id: "companyRegistration.servicesProvided.badge",
          })}
          badgeBgColor="bg-green-100"
          badgeTextColor="text-green-200"
          title={intl.formatMessage({
            id: "companyRegistration.servicesProvided.title",
          })}
        />

        {/* Services Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 lg:gap-8 mt-8 md:mt-12">
          {ServicesData?.map((item, index) => (
            <div
              key={index}
              className="bg-white rounded-xl md:rounded-2xl hover:shadow-xl transition-all duration-300 p-4 md:p-6 lg:p-8 h-full flex flex-col items-start group hover:-translate-y-1"
            >
              {/* Service Icon */}
              <div className="flex justify-center mb-4 md:mb-6">
                <div className="relative w-50 h-50">
                  <Image
                    src={item.icon}
                    alt={item.title}
                    width={150}
                    height={150}
                    className="w-full h-full object-contain transition-transform duration-300 group-hover:scale-105"
                  />
                </div>
              </div>

              {/* Service Title */}
              <h3 className="font-semibold text-lg sm:text-xl md:text-1xl lg:text-1xl xl:text-2xl mb-3 md:mb-4">
                {item.title}
              </h3>

              {/* Service Description */}
              <p className="font-normal flex-grow">{item.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ServicesProvidedSection;
