import React from "react";
import AppHeader from "@/components/AppHeader";
import AppFooter from "@/components/AppFooter";

const GlobalContainer = ({
  children,
  headerDark = false,
}: {
  children: React.ReactNode;
  headerDark?: boolean;
}) => {
  return (
    <div>
      <AppHeader dark={headerDark} />
      <div>{children}</div>
      <AppFooter />
    </div>
  );
};

export default GlobalContainer;
