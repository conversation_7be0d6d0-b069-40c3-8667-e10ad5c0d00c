import React from "react";
import { useIntl } from "react-intl";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { Button } from "../ui/button";

export default function FeatureServicesSection() {
  const intl = useIntl();

  return (
    <section className={cn("bg-dark-100 text-white py-8 md:py-16 lg:py-20")}>
      <div className="custom-container">
        <div className="flex flex-col gap-8 lg:gap-12 xl:gap-16 lg:flex-row lg:items-start lg:justify-between">
          {/* Content Section */}
          <div className="flex-1 lg:max-w-[566px] xl:max-w-[600px]">
            <div className="flex items-center font-normal text-sm md:text-base mb-4 md:mb-6">
              <span>
                {intl.formatMessage({
                  id: "bookkeeping.services.preTitle1",
                })}
              </span>
              <div className="w-1 h-1 bg-white rounded-full mx-2.5" />
              <span>
                {intl.formatMessage({
                  id: "bookkeeping.services.preTitle2",
                })}
              </span>
            </div>

            <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-black leading-tight mb-4 md:mb-6 lg:mb-8">
              {intl.formatMessage({ id: "bookkeeping.services.title" })}
            </h1>

            <div className="text-sm md:text-base lg:text-lg leading-relaxed mb-8 md:mb-10 lg:mb-12 text-gray-200">
              {intl.formatMessage({
                id: "bookkeeping.services.description",
              })}
            </div>

            {/* Buttons Section */}
            <div className="flex flex-col sm:flex-row gap-3 md:gap-4">
              <Button
                className="w-full sm:w-auto px-6 md:px-8 py-3 md:py-4 text-sm md:text-base font-medium"
                size="lg"
              >
                {intl.formatMessage({
                  id: "bookkeeping.services.primaryBtnText",
                })}
              </Button>
              <Button
                variant="outline"
                className="w-full sm:w-auto px-6 md:px-8 py-3 md:py-4 text-sm md:text-base font-medium bg-transparent border-primary text-primary hover:bg-primary hover:text-dark-100"
                size="lg"
              >
                {intl.formatMessage({
                  id: "bookkeeping.services.secondaryBtnText",
                })}
              </Button>
            </div>
          </div>

          {/* Image Section */}
          <div className="flex-shrink-0 lg:flex-1 lg:max-w-[610px] xl:max-w-[650px]">
            <div className="relative w-full">
              <Image
                src={"/imgs/bookkeeping-service.png"}
                alt="Feature Services"
                width={610}
                height={578}
                className="w-full h-auto max-w-full rounded-xl md:rounded-2xl object-cover shadow-lg"
                priority
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
