import React from "react";
import { useIntl } from "react-intl";
import Image from "next/image";

const ContentSection = () => {
  const intl = useIntl();

  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="custom-container">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-center">
          {/* Text Content */}
          <div className="order-2 lg:order-1">
            <div className="space-y-6">
              <p className="text-base md:text-lg text-gray-700 leading-relaxed">
                {intl.formatMessage({ id: "aboutUs.content.paragraph1" })}
              </p>
              <p className="text-base md:text-lg text-gray-700 leading-relaxed">
                {intl.formatMessage({ id: "aboutUs.content.paragraph2" })}
              </p>
            </div>
          </div>

          {/* Image */}
          <div className="order-1 lg:order-2">
            <div className="relative">
              <Image
                src="/imgs/content.png"
                alt="About Us content image"
                width={600}
                height={400}
                className="w-full h-auto rounded-2xl object-cover"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContentSection;
