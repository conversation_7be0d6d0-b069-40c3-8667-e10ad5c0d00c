import React, { useState } from "react";
import { useIntl } from "react-intl";
import { Check } from "lucide-react";
import { Button } from "../ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import BadgeHeader from "../BadgeHeader";
import { Separator } from "../ui/separator";

export default function Pricing() {
  const intl = useIntl();
  const [selectedCurrency, setSelectedCurrency] = useState("HK$");

  // Package data using internationalization
  const packageData = {
    name: intl.formatMessage({
      id: "bookkeeping.pricing.greenBox.name",
    }),
    subtitle: intl.formatMessage({
      id: "bookkeeping.pricing.greenBox.subtitle",
    }),
    originalPrice: intl.formatMessage({
      id: "bookkeeping.pricing.greenBox.originalPrice",
    }),
    currentPrice: intl.formatMessage({
      id: "bookkeeping.pricing.greenBox.currentPrice",
    }),
    buttonText: intl.formatMessage({
      id: "bookkeeping.pricing.greenBox.buttonText",
    }),
    incorporation: [
      intl.formatMessage({
        id: "bookkeeping.pricing.incorporation.item1",
      }),
      intl.formatMessage({
        id: "bookkeeping.pricing.incorporation.item2",
      }),
      intl.formatMessage({
        id: "bookkeeping.pricing.incorporation.item3",
      }),
      intl.formatMessage({
        id: "bookkeeping.pricing.incorporation.item4",
      }),
      intl.formatMessage({
        id: "bookkeeping.pricing.incorporation.item5",
      }),
      intl.formatMessage({
        id: "bookkeeping.pricing.incorporation.item6",
      }),
      intl.formatMessage({
        id: "bookkeeping.pricing.incorporation.item7",
      }),
      intl.formatMessage({
        id: "bookkeeping.pricing.incorporation.item8",
      }),
      intl.formatMessage({
        id: "bookkeeping.pricing.incorporation.item9",
      }),
      intl.formatMessage({
        id: "bookkeeping.pricing.incorporation.item10",
      }),
    ],
    included: [
      intl.formatMessage({
        id: "bookkeeping.pricing.included.item1",
      }),
      intl.formatMessage({
        id: "bookkeeping.pricing.included.item2",
      }),
    ],
    optionalAddOns: [
      {
        title: intl.formatMessage({
          id: "bookkeeping.pricing.addons.secretary.title",
        }),
        price: intl.formatMessage({
          id: "bookkeeping.pricing.addons.secretary.price",
        }),
        features: [
          intl.formatMessage({
            id: "bookkeeping.pricing.addons.secretary.feature1",
          }),
          intl.formatMessage({
            id: "bookkeeping.pricing.addons.secretary.feature2",
          }),
          intl.formatMessage({
            id: "bookkeeping.pricing.addons.secretary.feature3",
          }),
        ],
      },
      {
        title: intl.formatMessage({
          id: "bookkeeping.pricing.addons.address.title",
        }),
        price: intl.formatMessage({
          id: "bookkeeping.pricing.addons.address.price",
        }),
        features: [
          intl.formatMessage({
            id: "bookkeeping.pricing.addons.address.feature1",
          }),
          intl.formatMessage({
            id: "bookkeeping.pricing.addons.address.feature2",
          }),
          intl.formatMessage({
            id: "bookkeeping.pricing.addons.address.feature3",
          }),
        ],
      },
    ],
  };

  return (
    <section className="py-12 md:py-16 lg:py-20 xl:py-24">
      <div className="custom-container">
        <div>
          <BadgeHeader
            badgeText={intl.formatMessage({
              id: "bookkeeping.pricing.badge",
            })}
            badgeBgColor="bg-green-100"
            badgeTextColor="text-green-200"
            title={intl.formatMessage({
              id: "bookkeeping.pricing.title",
            })}
            subTitle={intl.formatMessage({
              id: "bookkeeping.pricing.description",
            })}
          />

          {/* Currency Selector */}
          <div className="flex items-center justify-center">
            <div className="flex items-center gap-2 sm:gap-3">
              <Select
                value={selectedCurrency}
                onValueChange={setSelectedCurrency}
              >
                <SelectTrigger className="w-24 sm:w-28">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="HK$">HK$ HKD</SelectItem>
                  <SelectItem value="USD">USD USD</SelectItem>
                  <SelectItem value="CNY">CNY CNY</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Pricing Card */}
        <div className="max-w-2xl mx-auto mt-8">
          <Card className="bg-white border-0 rounded-2xl overflow-hidden !shadow-none">
            <CardHeader className="p-6 sm:p-8 md:p-10">
              <CardTitle className="text-xl sm:text-2xl md:text-3xl font-bold  mb-2">
                {packageData.name}
              </CardTitle>
              <p className="text-sm sm:text-base mb-6 sm:mb-8">
                {packageData.subtitle}
              </p>

              {/* Pricing */}
              <div>
                <div className="flex items-baseline gap-2 mb-4">
                  <span className="text-sm sm:text-base line-through">
                    {selectedCurrency}
                    {packageData.originalPrice}
                  </span>
                </div>
                <div className="flex items-end gap-2 mb-6">
                  <span className="font-bold text-2xl">{selectedCurrency}</span>
                  <span className="text-3xl sm:text-4xl md:text-5xl font-bold ">
                    {packageData.currentPrice}
                  </span>
                </div>

                {/* CTA Button */}
                <Button className="w-full bg-primary py-3 sm:py-4 text-sm sm:text-base font-medium rounded-lg">
                  {packageData.buttonText}
                </Button>
              </div>
            </CardHeader>
            <div className="px-6">
              <Separator />
            </div>
            <CardContent className="p-6 sm:p-8 md:p-10 pt-0">
              {/* Incorporation Section */}
              <div className="mb-4 sm:mb-6">
                <h3 className="text-lg sm:text-xl font-bold mb-4 sm:mb-6">
                  {intl.formatMessage({
                    id: "bookkeeping.pricing.incorporation.title",
                  })}
                </h3>
                <div className="space-y-3 sm:space-y-4">
                  {packageData.incorporation.map((item, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <div className="flex-shrink-0 mt-0.5">
                        <Check className="w-4 h-4 sm:w-5 sm:h-5 " />
                      </div>
                      <span className="text-sm sm:text-base">{item}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Included Section */}
              <div className="mb-8 sm:mb-10">
                <h3 className="text-lg sm:text-xl font-bold mb-4 sm:mb-6">
                  {intl.formatMessage({
                    id: "bookkeeping.pricing.included.title",
                  })}
                </h3>
                <div className="space-y-3 sm:space-y-4">
                  {packageData.included.map((item, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <div className="flex-shrink-0 mt-0.5">
                        <Check className="w-4 h-4 sm:w-5 sm:h-5 " />
                      </div>
                      <span className="text-sm sm:text-base">{item}</span>
                    </div>
                  ))}
                </div>
                <Separator className="mt-9" />
              </div>

              {/* Optional Add-ons Section */}
              <div>
                <h3 className="text-lg sm:text-xl font-bold mb-4 sm:mb-6">
                  {intl.formatMessage({
                    id: "bookkeeping.pricing.addons.title",
                  })}
                </h3>
                <div className="space-y-6 sm:space-y-8">
                  {packageData.optionalAddOns.map((addon, index) => (
                    <div key={index}>
                      <div className="flex items-start justify-between mb-3 sm:mb-4">
                        <h4 className="text-base sm:text-lg font-semibold">
                          {addon.title}
                        </h4>
                        <span className="text-base sm:text-lg ml-4">
                          {addon.price}
                        </span>
                      </div>
                      <div className="space-y-2 sm:space-y-3">
                        {addon.features.map((feature, featureIndex) => (
                          <div
                            key={featureIndex}
                            className="flex items-start gap-3"
                          >
                            <div className="flex-shrink-0 mt-0.5">
                              <Check className="w-4 h-4 sm:w-5 sm:h-5 " />
                            </div>
                            <span className="text-sm sm:text-base leading-relaxed">
                              {feature}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}
