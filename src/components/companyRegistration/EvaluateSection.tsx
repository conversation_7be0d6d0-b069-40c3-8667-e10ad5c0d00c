import Image from "next/image";
import React from "react";
import { useIntl } from "react-intl";

export default function EvaluateSection() {
  const intl = useIntl();
  return (
    <section className="bg-white">
      <div className="custom-container py-12 md:py-20 lg:py-24 xl:py-28">
        <div className="flex flex-col lg:flex-row gap-8 md:gap-10 lg:gap-12 xl:gap-16 items-center lg:items-start">
          {/* Image Section */}
          <div className="w-full lg:flex-1 lg:max-w-125">
            <div className="relative w-full lg:mx-0 lg:max-w-none">
              <div className="w-full">
                <Image
                  src="/imgs/founder1.png"
                  alt={intl.formatMessage({
                    id: "companyRegistration.evaluate.testimonial.imageAlt",
                  })}
                  width={754}
                  height={1005}
                  className="rounded-2xl md:rounded-[20px] w-full object-cover shadow-lg"
                  priority
                />
              </div>
            </div>
          </div>

          {/* Content Section */}
          <div className="w-full lg:flex-1 flex flex-col justify-center">
            {/* Quote Icon */}
            <div className="mb-4 md:mb-6">
              <Image
                src="/icons/quote.svg"
                alt={intl.formatMessage({
                  id: "companyRegistration.evaluate.testimonial.quoteIconAlt",
                })}
                width={24}
                height={24}
                className="w-6 h-6 md:w-8 md:h-8 lg:w-10 lg:h-10"
              />
            </div>

            {/* Testimonial Text */}
            <div className="text-lg sm:text-xl md:text-1xl lg:text-2xl xl:text-3xl font-medium md:font-semibold mb-6 md:mb-8 lg:mb-10 xl:mb-11">
              {intl.formatMessage({
                id: "companyRegistration.evaluate.testimonial.quote",
              })}
            </div>

            {/* Author Information */}
            <div className="space-y-1 md:space-y-2">
              <div className="text-lg sm:text-xl md:text-2xl lg:text-2xl font-medium md:font-bold">
                {intl.formatMessage({
                  id: "companyRegistration.evaluate.testimonial.name",
                })}
              </div>
              <div className="text-sm sm:text-base md:text-lg font-normal md:font-medium">
                {intl.formatMessage({
                  id: "companyRegistration.evaluate.testimonial.title",
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
