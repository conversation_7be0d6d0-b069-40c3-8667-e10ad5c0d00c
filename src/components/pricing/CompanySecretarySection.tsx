import React from "react";
import { Button } from "@/components/ui/button";
import { Check } from "lucide-react";

interface ServiceItem {
  name: string;
  included: boolean;
}

export default function CompanySecretarySection() {
  const serviceItems: ServiceItem[] = [
    { name: "Excludes government fees", included: true },
    { name: "Act as Company Secretary", included: true },
    { name: "Submit Annual Return (NAR1)", included: true },
  ];

  return (
    <div className="w-full max-w-7xl mx-auto p-6 ">
      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        {/* Header Section */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 p-6 lg:p-8">
          {/* Left Side - Service Description */}
          <div className="lg:col-span-2 space-y-4">
            <h1 className="text-3xl lg:text-4xl font-bold text-gray-900">
              Company Secretary Service
            </h1>
            <p className="text-gray-600 text-base lg:text-lg">
              For those who already have a Hong Kong office address
            </p>
          </div>

          {/* Right Side - Pricing Card */}
          <div className="lg:col-span-1 flex justify-center lg:justify-end">
            <div className="w-full max-w-sm">
              <div className="text-center pb-4">
                <div className="text-xl lg:text-2xl font-bold text-gray-900 mb-4">
                  Basic Plan
                </div>
                <div className="space-y-2">
                  <p className="text-sm text-gray-600">Basic Price</p>
                  <p className="text-3xl lg:text-4xl font-bold text-gray-900">
                    HK$1,500
                  </p>
                </div>
              </div>
              <div className="pt-0">
                <Button
                  className="w-full bg-blue-400 hover:bg-blue-500 text-white font-medium py-3 px-6 rounded-xl transition-colors duration-200"
                  variant="default"
                >
                  Get this plan
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Services Section */}
        <div className="px-6 lg:px-8 pb-6 lg:pb-8">
          <div className="space-y-6">
            <h2 className="text-xl lg:text-2xl font-semibold text-gray-900 border-b pb-3">
              Incorporation
            </h2>

            <div className="space-y-4">
              {serviceItems.map((service, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between py-4 border-b border-gray-100 last:border-b-0"
                >
                  <div className="flex-1">
                    <p className="text-base lg:text-lg font-medium text-gray-700">
                      {service.name}
                    </p>
                  </div>
                  <div className="flex justify-center items-center ml-4">
                    {service.included && (
                      <Check className="h-6 w-6 text-green-600" />
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
