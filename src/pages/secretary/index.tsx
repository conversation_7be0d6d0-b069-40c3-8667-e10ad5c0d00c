import React from "react";
import GlobalContainer from "@/components/GlobalContainer";
import FeatureServicesSection from "@/components/secretary/FeatureServicesSection";
import EvaluateSection from "@/components/secretary/EvaluateSection";
import ServicesProvidedSection from "@/components/secretary/ServicesProvidedSection";
import HelpSection from "@/components/secretary/HelpSection";
import ChooseUsSection from "@/components/secretary/ChooseUsSection";
import LaunchSection from "@/components/secretary/ScheduleACallSection";
import StepsSection from "@/components/secretary/StepsSection";
import Pricing from "@/components/secretary/Pricing";
import FAQs from "@/components/secretary/FAQs";
import PartnerNetworkSection from "@/components/secretary/PartnerNetworkSection";

export default function Features() {
  return (
    <GlobalContainer headerDark={true}>
      <FeatureServicesSection />
      <EvaluateSection />
      <ServicesProvidedSection />
      <HelpSection />
      <ChooseUsSection />
      <LaunchSection />
      <StepsSection />
      <Pricing />
      <PartnerNetworkSection />
      <FAQs />
    </GlobalContainer>
  );
}
