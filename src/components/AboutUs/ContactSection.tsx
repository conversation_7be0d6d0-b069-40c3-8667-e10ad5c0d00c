import React, { useState } from "react";
import { useIntl } from "react-intl";
import Link from "next/link";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import BadgeHeader from "@/components/BadgeHeader";

const ContactSection = () => {
  const intl = useIntl();
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    interestedIn: "",
    message: "",
  });

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Form submitted:", formData);
  };

  return (
    <section className="lg:mt-20 mt-10 md:mb-28 mb-14">
      <div className="custom-container">
        <BadgeHeader
          badgeText={intl.formatMessage({ id: "aboutUs.contact.badge" })}
          badgeBgColor="bg-green-100"
          badgeTextColor="text-green-200"
          title={intl.formatMessage({ id: "aboutUs.contact.title" })}
          subTitle={intl.formatMessage({ id: "aboutUs.contact.description" })}
          subTitleCls="max-w-4xl mx-auto"
        />

        <div className="max-w-4xl mx-auto">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Name and Email */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Input
                  type="text"
                  name="name"
                  placeholder={intl.formatMessage({
                    id: "aboutUs.contact.form.namePlaceholder",
                  })}
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div>
                <Input
                  type="email"
                  name="email"
                  placeholder={intl.formatMessage({
                    id: "aboutUs.contact.form.emailPlaceholder",
                  })}
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                />
              </div>
            </div>

            {/* Phone and Interested In */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="relative">
                <div className="absolute left-3 top-1/2 -translate-y-1/2 flex items-center">
                  <img
                    src="/imgs/uil_arrow.png"
                    alt="HK"
                    className="w-5 object-cover mr-2"
                  />
                  <img
                    src="/imgs/hk-flag.png"
                    alt="HK"
                    className="w-5 object-cover mr-2"
                  />
                  <div className="border-l border-gray-200 h-5 w-1 mx-1"></div>
                  <span className="text-sm text-black">+852</span>
                </div>
                <Input
                  type="tel"
                  name="phone"
                  placeholder={intl.formatMessage({
                    id: "aboutUs.contact.form.phonePlaceholder",
                  })}
                  value={formData.phone}
                  onChange={handleInputChange}
                  className="pl-30"
                  required
                />
              </div>
              <div>
                <Select>
                  <SelectTrigger className="w-full !h-12">
                    <SelectValue
                      placeholder={intl.formatMessage({
                        id: "aboutUs.contact.form.interestedPlaceholder",
                      })}
                    />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectLabel>
                        {intl.formatMessage({
                          id: "aboutUs.contact.form.interestedLabel",
                        })}
                      </SelectLabel>
                      <SelectItem value="companyRegistration">
                        {intl.formatMessage({
                          id: "common.navigation.companyRegistration",
                        })}
                      </SelectItem>
                      <SelectItem value="bookkeeping">
                        {intl.formatMessage({
                          id: "common.navigation.bookkeeping",
                        })}
                      </SelectItem>
                      <SelectItem value="accountingAudit">
                        {intl.formatMessage({
                          id: "common.navigation.accountingAudit",
                        })}
                      </SelectItem>
                      <SelectItem value="secretary">
                        {intl.formatMessage({
                          id: "common.navigation.secretary",
                        })}
                      </SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Message */}
            <div>
              <Textarea
                name="message"
                placeholder={intl.formatMessage({
                  id: "aboutUs.contact.form.messagePlaceholder",
                })}
                value={formData.message}
                onChange={handleInputChange}
                required
                className="resize-none h-60 p-4"
              />
            </div>

            {/* Submit Button */}
            <div className="text-center md:mt-16">
              <Button
                type="submit"
                className="px-8 py-3 rounded-md font-medium"
              >
                {intl.formatMessage({
                  id: "aboutUs.contact.form.submitButton",
                })}
              </Button>
            </div>

            {/* Terms and Privacy */}
            <div className="text-center text-sm">
              {intl.formatMessage({ id: "aboutUs.contact.form.termsText" })}{" "}
              <Link href="/terms" className="underline">
                {intl.formatMessage({ id: "aboutUs.contact.form.termsLink" })}
              </Link>{" "}
              <Link href="/privacy" className="underline">
                {intl.formatMessage({ id: "aboutUs.contact.form.privacyLink" })}
              </Link>{" "}
              {intl.formatMessage({ id: "aboutUs.contact.form.and" })}{" "}
              <Link href="/data-protection" className="underline">
                {intl.formatMessage({
                  id: "aboutUs.contact.form.dataProtectionLink",
                })}
              </Link>
            </div>
          </form>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
