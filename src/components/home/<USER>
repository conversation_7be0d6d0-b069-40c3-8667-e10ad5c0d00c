import React from "react";
import { useIntl } from "react-intl";
import Image from "next/image";
import { ArrowRight } from "lucide-react";

interface ServiceCardProps {
  title: string;
  description: string;
  buttonText: string;
  imageSrc: string;
  imageAlt: string;
  onButtonClick?: () => void;
}

const ServiceCard: React.FC<ServiceCardProps> = ({
  title,
  description,
  buttonText,
  imageSrc,
  imageAlt,
  onButtonClick,
}) => {
  return (
    <div className="bg-white rounded-xl md:rounded-3xl p-6 md:p-4 h-full flex flex-col">
      {/* Service Icon */}
      <div className="flex justify-center">
        <div className="w-50 h-50 relative">
          <Image
            src={imageSrc}
            alt={imageAlt}
            fill
            className="object-cover w-full h-full"
          />
        </div>
      </div>

      {/* Service Content */}
      <div className="flex-1 flex flex-col gap-2">
        <h3 className="text-2xl md:text-2xl font-bold text-gray-900 md:mb-4">
          {title}
        </h3>

        <p className="text-base mb-4 flex-1">{description}</p>

        {/* Action Button */}
        <div>
          <div
            className="w-fit px-4 cursor-pointer flex items-center justify-center gap-2 py-3 border border-primary rounded-full text-primary font-semibold"
            onClick={onButtonClick}
          >
            {buttonText}
            <ArrowRight className="w-6 h-6" />
          </div>
        </div>
      </div>
    </div>
  );
};

const ServicesSection: React.FC = () => {
  const intl = useIntl();

  const services = [
    {
      title: intl.formatMessage({
        id: "home.services.companyRegistration.title",
      }),
      description: intl.formatMessage({
        id: "home.services.companyRegistration.description",
      }),
      buttonText: intl.formatMessage({
        id: "home.services.companyRegistration.button",
      }),
      imageSrc: "/imgs/companyRegistration.png",
      imageAlt: "Company Registration",
    },
    {
      title: intl.formatMessage({ id: "home.services.bookkeeping.title" }),
      description: intl.formatMessage({
        id: "home.services.bookkeeping.description",
      }),
      buttonText: intl.formatMessage({
        id: "home.services.bookkeeping.button",
      }),
      imageSrc: "/imgs/bookkeepingServices.png",
      imageAlt: "Bookkeeping Services",
    },
    {
      title: intl.formatMessage({ id: "home.services.companySecretary.title" }),
      description: intl.formatMessage({
        id: "home.services.companySecretary.description",
      }),
      buttonText: intl.formatMessage({
        id: "home.services.companySecretary.button",
      }),
      imageSrc: "/imgs/companySecretary.png",
      imageAlt: "Company Secretary",
    },
    {
      title: intl.formatMessage({ id: "home.services.auditingTaxation.title" }),
      description: intl.formatMessage({
        id: "home.services.auditingTaxation.description",
      }),
      buttonText: intl.formatMessage({
        id: "home.services.auditingTaxation.button",
      }),
      imageSrc: "/imgs/auditingAndTaxation.png",
      imageAlt: "Auditing and Taxation",
    },
  ];

  return (
    <section className="py-12 md:py-20 lg:py-24 bg-dark-100">
      <div className="custom-container">
        {/* Section Header */}
        <div className="mb-8 md:mb-16 lg:space-y-0 md:space-y-8 space-y-4">
          <h2 className="text-center lg:text-left lg:w-1/2 text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold text-white">
            <div>{intl.formatMessage({ id: "home.services.title" })} </div>
            <span className="text-primary">
              {intl.formatMessage({ id: "home.services.titleHighlight" })}
            </span>
          </h2>

          <div className="flex justify-end">
            <div className="text-center lg:text-left lg:w-1/2 text-white text-base md:text-lg lg:text-xl">
              <div className="mb-4">
                {intl.formatMessage({ id: "home.services.description1" })}
              </div>
              {intl.formatMessage({ id: "home.services.description" })}{" "}
              <span className="text-primary cursor-pointer transition-colors">
                {intl.formatMessage({ id: "home.services.descriptionLink" })}
              </span>
            </div>
          </div>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-3 md:gap-5">
          {services.map((service, index) => (
            <ServiceCard
              key={index}
              title={service.title}
              description={service.description}
              buttonText={service.buttonText}
              imageSrc={service.imageSrc}
              imageAlt={service.imageAlt}
              onButtonClick={() => {}}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;
