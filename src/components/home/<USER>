import React from "react";
import { useIntl } from "react-intl";
import Image from "next/image";

import BadgeHeader from "../BadgeHeader";

const TestimonialsSection = () => {
  const intl = useIntl();

  return (
    <div className="custom-container py-12 md:py-20 lg:py-24 xl:py-28">
      <BadgeHeader
        badgeText={intl.formatMessage({ id: "home.testimonials.badge" })}
        badgeBgColor="bg-purple-100"
        badgeTextColor="text-purple-200"
        title={intl.formatMessage({ id: "home.testimonials.title" })}
        titleHighlight={intl.formatMessage({
          id: "home.testimonials.titleHighlight",
        })}
      />

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 rounded-2xl overflow-hidden shadow-sm">
          <div className="relative h-64 md:h-80">
            <Image
              src="/imgs/testimonials-ceo.jpg"
              alt="ceo working on laptop"
              fill
              className="object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-b from-black/10 via-black/20 to-black/50"></div>
            <div className="absolute inset-0 flex items-end lg:w-[75%] w-full">
              <div className="p-6 text-white">
                <blockquote className="md:text-xl font-medium mb-3">
                  &quot;
                  {intl.formatMessage({
                    id: "home.testimonials.sarah.quote",
                  })}
                  &quot;
                </blockquote>
                <div className="text-sm opacity-90">
                  {intl.formatMessage({ id: "home.testimonials.sarah.name" })}{" "}
                  {intl.formatMessage({
                    id: "home.testimonials.sarah.title",
                  })}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-blue-100 rounded-2xl p-8 flex flex-col lg:justify-end lg:items-start justify-center items-center text-white">
          <div className="text-6xl md:text-7xl font-bold mb-4">
            {intl.formatMessage({ id: "home.testimonials.stats.percentage" })}
          </div>
          <div className="text-lg md:text-xl">
            {intl.formatMessage({
              id: "home.testimonials.stats.description",
            })}
          </div>
        </div>

        <div className="bg-dark-100 rounded-2xl p-8 flex flex-col lg:justify-end lg:items-start justify-center items-center text-white text-center">
          <div className="text-6xl md:text-7xl font-bold mb-4">
            {intl.formatMessage({ id: "home.testimonials.cases.number" })}
          </div>
          <div className="text-lg md:text-xl">
            {intl.formatMessage({
              id: "home.testimonials.cases.description",
            })}
          </div>
        </div>

        <div className="lg:col-span-2 bg-white rounded-2xl overflow-hidden shadow-sm">
          <div className="relative h-64 md:h-80">
            <Image
              src="/imgs/testimonials-managing.jpg"
              alt="managing director in office environment"
              fill
              className="object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-b from-black/10 via-black/20 to-black/50"></div>
            <div className="absolute inset-0 flex items-end lg:w-[75%] w-full">
              <div className="p-6 text-white">
                <blockquote className="md:text-xl font-medium mb-3">
                  &quot;
                  {intl.formatMessage({
                    id: "home.testimonials.david.quote",
                  })}
                  &quot;
                </blockquote>
                <div className="text-sm opacity-90">
                  {intl.formatMessage({ id: "home.testimonials.david.name" })}{" "}
                  {intl.formatMessage({
                    id: "home.testimonials.david.title",
                  })}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestimonialsSection;
