import React, { useState } from "react";
import { useIntl } from "react-intl";
import { Check } from "lucide-react";
import { Button } from "../ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import BadgeHeader from "../BadgeHeader";
import { Separator } from "../ui/separator";

export default function Pricing() {
  const intl = useIntl();
  const [selectedCurrency, setSelectedCurrency] = useState("HK$");

  // Package data using internationalization
  const packageData = {
    name: intl.formatMessage({
      id: "accountingAudit.pricing.basicPlan",
    }),
    subtitle: intl.formatMessage({
      id: "accountingAudit.pricing.basicPlan.subtitle",
    }),
    currentPrice: intl.formatMessage({
      id: "accountingAudit.pricing.basicPlan.currentPrice",
    }),
    buttonText: intl.formatMessage({
      id: "accountingAudit.pricing.basicPlan.buttonText",
    }),
    incorporation: [
      intl.formatMessage({
        id: "accountingAudit.pricing.incorporation.item1",
      }),
      intl.formatMessage({
        id: "accountingAudit.pricing.incorporation.item2",
      }),
      intl.formatMessage({
        id: "accountingAudit.pricing.incorporation.item3",
      }),
    ],
  };

  return (
    <section className="py-12 md:py-16 lg:py-20 xl:py-24">
      <div className="custom-container">
        <div>
          <BadgeHeader
            badgeText={intl.formatMessage({
              id: "accountingAudit.pricing.badge",
            })}
            badgeBgColor="bg-green-100"
            badgeTextColor="text-green-200"
            title={intl.formatMessage({
              id: "accountingAudit.pricing.title",
            })}
            subTitle={intl.formatMessage({
              id: "accountingAudit.pricing.description",
            })}
          />

          {/* Currency Selector */}
          <div className="flex items-center justify-center">
            <div className="flex items-center gap-2 sm:gap-3">
              <Select
                value={selectedCurrency}
                onValueChange={setSelectedCurrency}
              >
                <SelectTrigger className="w-24 sm:w-28">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="HK$">HK$ HKD</SelectItem>
                  <SelectItem value="USD">USD USD</SelectItem>
                  <SelectItem value="CNY">CNY CNY</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Pricing Card */}
        <div className="max-w-2xl mx-auto mt-8">
          <Card className="bg-white border-0 rounded-2xl overflow-hidden !shadow-none">
            <CardHeader className="p-6 sm:p-8 md:p-10">
              <CardTitle className="text-xl sm:text-2xl md:text-3xl font-bold  mb-2">
                {packageData.name}
              </CardTitle>
              <p className="text-sm sm:text-base mb-6 sm:mb-8">
                {packageData.subtitle}
              </p>

              {/* Pricing */}
              <div>
                <div className="flex items-end gap-2 mb-6">
                  <span className="font-bold text-2xl">{selectedCurrency}</span>
                  <span className="text-3xl sm:text-4xl md:text-5xl font-bold ">
                    {packageData.currentPrice}
                  </span>
                </div>

                {/* CTA Button */}
                <Button className="w-full bg-primary py-3 sm:py-4 text-sm sm:text-base font-medium rounded-lg">
                  {packageData.buttonText}
                </Button>
              </div>
            </CardHeader>
            <div className="px-6">
              <Separator />
            </div>
            <CardContent className="p-6 sm:p-8 md:p-10 pt-0">
              {/* Incorporation Section */}
              <div className="mb-4 sm:mb-6">
                <h3 className="text-lg sm:text-xl font-bold mb-4 sm:mb-6">
                  {intl.formatMessage({
                    id: "accountingAudit.pricing.incorporation.title",
                  })}
                </h3>
                <div className="space-y-3 sm:space-y-4">
                  {packageData.incorporation.map((item, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <div className="flex-shrink-0 mt-0.5">
                        <Check className="w-4 h-4 sm:w-5 sm:h-5 " />
                      </div>
                      <span className="text-sm sm:text-base">{item}</span>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}
