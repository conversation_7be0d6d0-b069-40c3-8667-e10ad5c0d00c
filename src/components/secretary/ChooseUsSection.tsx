import React from "react";
import { useIntl } from "react-intl";
import Image from "next/image";

import BadgeHeader from "../BadgeHeader";

const ChooseUsSectionCard = ({ item }: { item: Record<string, string> }) => (
  <div className="flex gap-3 md:gap-4 lg:gap-5 bg-white rounded-xl p-4 md:p-6 lg:p-7 h-auto ">
    <div className="flex-shrink-0">
      <Image
        src="/icons/check.svg"
        alt="check"
        width={48}
        height={48}
        className="w-8 h-8 md:w-10 md:h-10"
      />
    </div>
    <div className="flex-1">
      <h3 className="text-base md:text-lg lg:text-xl xl:text-2xl font-bold mb-1 md:mb-2 lg:mb-3">
        {item.title}
        <br />
        {item.subTitle}
      </h3>
      <p className="text-sm md:text-base xl:text-lg">{item.description}</p>
    </div>
  </div>
);

const ChooseUsSection = () => {
  const intl = useIntl();
  const firstRow = [
    {
      title: intl.formatMessage({
        id: "secretary.whyChooseUs.card1.title",
      }),
      subTitle: intl.formatMessage({
        id: "secretary.whyChooseUs.card1.subTitle",
      }),
      description: intl.formatMessage({
        id: "secretary.whyChooseUs.card1.description",
      }),
    },
    {
      title: intl.formatMessage({
        id: "secretary.whyChooseUs.card2.title",
      }),
      subTitle: intl.formatMessage({
        id: "secretary.whyChooseUs.card2.subTitle",
      }),
      description: intl.formatMessage({
        id: "secretary.whyChooseUs.card2.description",
      }),
    },
    {
      title: intl.formatMessage({
        id: "secretary.whyChooseUs.card3.title",
      }),
      subTitle: intl.formatMessage({
        id: "secretary.whyChooseUs.card3.subTitle",
      }),
      description: intl.formatMessage({
        id: "secretary.whyChooseUs.card3.description",
      }),
    },
  ];
  const secondRow = [
    {
      title: intl.formatMessage({
        id: "secretary.whyChooseUs.card4.title",
      }),
      subTitle: intl.formatMessage({
        id: "secretary.whyChooseUs.card4.subTitle",
      }),
      description: intl.formatMessage({
        id: "secretary.whyChooseUs.card4.description",
      }),
    },
    {
      title: intl.formatMessage({
        id: "secretary.whyChooseUs.card5.title",
      }),
      subTitle: intl.formatMessage({
        id: "secretary.whyChooseUs.card5.sutTitle",
      }),
      description: intl.formatMessage({
        id: "secretary.whyChooseUs.card5.description",
      }),
    },
  ];

  return (
    <section className="py-12 md:py-16 lg:py-20 xl:py-24">
      <div className="custom-container">
        <BadgeHeader
          badgeText={intl.formatMessage({
            id: "secretary.whyChooseUs.badge",
          })}
          badgeBgColor="bg-purple-100"
          badgeTextColor="text-purple-200"
          title={intl.formatMessage({
            id: "secretary.whyChooseUs.title",
          })}
        />

        <div className="space-y-4 md:space-y-6 lg:space-y-8">
          <div className="block md:hidden">
            <div className="grid grid-cols-1 gap-3 md:gap-4">
              {[...firstRow, ...secondRow].map((item, index) => (
                <ChooseUsSectionCard key={index} item={item} />
              ))}
            </div>
          </div>

          <div className="hidden md:block lg:hidden">
            <div className="space-y-5 md:space-y-6">
              <div className="grid grid-cols-2 gap-4 md:gap-5 lg:gap-6">
                {[...firstRow, ...secondRow].slice(0, 4).map((item, index) => (
                  <ChooseUsSectionCard key={index} item={item} />
                ))}
              </div>

              {/* Last card centered */}
              <div className="flex justify-center">
                <div className="w-full max-w-sm md:max-w-md lg:max-w-lg">
                  <ChooseUsSectionCard
                    key={4}
                    item={[...firstRow, ...secondRow][4]}
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="hidden lg:block">
            <div className="grid grid-cols-3 gap-5 lg:gap-6 xl:gap-8 mb-6 lg:mb-8 xl:mb-10">
              {firstRow.map((item, index) => (
                <ChooseUsSectionCard key={index} item={item} />
              ))}
            </div>

            <div className="grid grid-cols-2 gap-5 lg:gap-6 xl:gap-8 max-w-3xl lg:max-w-4xl xl:max-w-5xl mx-auto">
              {secondRow.map((item, index) => (
                <ChooseUsSectionCard key={index + 3} item={item} />
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ChooseUsSection;
