/* eslint-disable @typescript-eslint/no-explicit-any */

import messagesEN from "@/languages/en";
import messages<PERSON><PERSON><PERSON><PERSON> from "@/languages/zh-Hans";
import messagesZHHANT from "@/languages/zh-Hant";

export const getLocaleMessages = (language: any) => {
  if (language === "en") {
    return messagesEN;
  } else if (language === "zh-Hant") {
    return messagesZHHANT;
  } else if (language === "zh-Hans") {
    return messagesZHHANS;
  }
  return messagesEN;
};

export const getLocaleTitle = (language: any) => {
  if (language === "en") {
    return "English";
  } else if (language === "zh-Hant") {
    return "繁體中文";
  } else if (language === "zh-Hans") {
    return "简体中文";
  }
  return "English";
};
