import React from "react";
import { useIntl } from "react-intl";
import router, { useRouter } from "next/router";
import { MenuIcon } from "lucide-react";

import { cn } from "@/lib/utils";
import { MenuBar } from "@/constants/app";
import { Button } from "./ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";

const BarItem = ({
  title,
  href,
  dark,
}: {
  title: string;
  href: string;
  dark: boolean;
}) => {
  const router = useRouter();
  return (
    <div className="text-center text-sm flex justify-center items-center gap-2 cursor-pointer">
      <Button
        variant="plain"
        className={cn("px-2 py-2", dark ? "text-white" : "")}
        onClick={() => router.push(href)}
      >
        {title}
      </Button>
    </div>
  );
};

const NavigationBar = ({ dark }: { dark: boolean }) => {
  const intl = useIntl();

  return (
    <>
      {/* Desktop */}
      <div
        className={cn(
          "hidden rounded-[12px] xl:flex justify-center items-center gap-6 px-4",
          dark ? "bg-secondary/10" : "bg-secondary"
        )}
      >
        {MenuBar.map((menu) => (
          <BarItem
            key={menu.name}
            title={intl.formatMessage({ id: `common.navigation.${menu.name}` })}
            href={menu.href}
            dark={dark}
          />
        ))}
      </div>

      {/* Mobile */}
      <div
        className={cn(
          "flex rounded-[12px] xl:hidden gap-6 items-center ",
          dark ? "bg-secondary/10" : "bg-secondary"
        )}
      >
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="plain"
              className={cn("px-2 py-2", dark ? "text-white" : "")}
            >
              <MenuIcon className="w-4 h-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            align="end"
            className="p-4 rounded-2xl shadow-lg border-0 bg-white w-64"
          >
            {MenuBar.map((menu) => (
              <DropdownMenuItem
                key={menu.name}
                className="px-4 py-3 text-base rounded-xl hover:bg-gray-50 cursor-pointer transition-colors"
                onClick={() => router.push(menu.href)}
              >
                {intl.formatMessage({ id: `common.navigation.${menu.name}` })}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </>
  );
};

export default NavigationBar;
