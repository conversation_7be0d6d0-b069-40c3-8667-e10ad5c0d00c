const fs = require('fs');
const path = require('path');

// 读取语言文件
function readLanguageFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  // 提取所有的key
  const keyRegex = /"([^"]+)":\s*"[^"]*"/g;
  const keys = [];
  let match;
  
  while ((match = keyRegex.exec(content)) !== null) {
    keys.push(match[1]);
  }
  
  return keys;
}

// 检查重复的key
function findDuplicateKeys(keys) {
  const keyCount = {};
  const duplicates = [];
  
  keys.forEach(key => {
    keyCount[key] = (keyCount[key] || 0) + 1;
    if (keyCount[key] === 2) {
      duplicates.push(key);
    }
  });
  
  return duplicates;
}

// 扫描代码文件中使用的key
function findUsedKeys(directory) {
  const usedKeys = new Set();
  
  function scanFile(filePath) {
    if (!fs.existsSync(filePath)) return;
    
    const content = fs.readFileSync(filePath, 'utf8');
    // 查找 formatMessage({ id: "key" }) 模式
    const keyRegex = /formatMessage\(\s*{\s*id:\s*["']([^"']+)["']/g;
    let match;
    
    while ((match = keyRegex.exec(content)) !== null) {
      usedKeys.add(match[1]);
    }
  }
  
  function scanDirectory(dir) {
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
        scanDirectory(filePath);
      } else if (file.endsWith('.tsx') || file.endsWith('.ts') || file.endsWith('.jsx') || file.endsWith('.js')) {
        scanFile(filePath);
      }
    });
  }
  
  scanDirectory(directory);
  return Array.from(usedKeys);
}

// 主函数
function analyzeLanguageFiles() {
  const enKeys = readLanguageFile('./src/languages/en.js');
  const zhHantKeys = readLanguageFile('./src/languages/zh-Hant.js');
  const usedKeys = findUsedKeys('./src');
  
  console.log('=== 英文文件 (en.js) 分析 ===');
  console.log(`总共 ${enKeys.length} 个key`);
  
  const enDuplicates = findDuplicateKeys(enKeys);
  if (enDuplicates.length > 0) {
    console.log('\n重复的key:');
    enDuplicates.forEach(key => console.log(`  - ${key}`));
  } else {
    console.log('\n✅ 没有重复的key');
  }
  
  console.log('\n=== 繁体中文文件 (zh-Hant.js) 分析 ===');
  console.log(`总共 ${zhHantKeys.length} 个key`);
  
  const zhHantDuplicates = findDuplicateKeys(zhHantKeys);
  if (zhHantDuplicates.length > 0) {
    console.log('\n重复的key:');
    zhHantDuplicates.forEach(key => console.log(`  - ${key}`));
  } else {
    console.log('\n✅ 没有重复的key');
  }
  
  console.log('\n=== 未使用的key分析 ===');
  const enUnused = enKeys.filter(key => !usedKeys.includes(key));
  const zhHantUnused = zhHantKeys.filter(key => !usedKeys.includes(key));
  
  if (enUnused.length > 0) {
    console.log(`\n英文文件中未使用的key (${enUnused.length}个):`);
    enUnused.forEach(key => console.log(`  - ${key}`));
  } else {
    console.log('\n✅ 英文文件中所有key都被使用');
  }
  
  if (zhHantUnused.length > 0) {
    console.log(`\n繁体中文文件中未使用的key (${zhHantUnused.length}个):`);
    zhHantUnused.forEach(key => console.log(`  - ${key}`));
  } else {
    console.log('\n✅ 繁体中文文件中所有key都被使用');
  }
  
  console.log(`\n=== 总结 ===`);
  console.log(`代码中使用的key总数: ${usedKeys.length}`);
  console.log(`英文文件key总数: ${enKeys.length}`);
  console.log(`繁体中文文件key总数: ${zhHantKeys.length}`);
}

analyzeLanguageFiles();
