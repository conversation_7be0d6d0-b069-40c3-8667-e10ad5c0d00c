import React from "react";
import { useIntl } from "react-intl";
import Image from "next/image";
import BadgeHeader from "../BadgeHeader";

const ServicesProvidedSection = () => {
  const intl = useIntl();

  const ServicesData = {
    audit: [
      {
        icon: "/imgs/accountingAudit/service1.png",
        title: intl.formatMessage({
          id: "accountingAudit.servicesProvided.title1",
        }),
        description: intl.formatMessage({
          id: "accountingAudit.servicesProvided.description1",
        }),
      },
      {
        icon: "/imgs/accountingAudit/service2.png",
        title: intl.formatMessage({
          id: "accountingAudit.servicesProvided.title2",
        }),
        description: intl.formatMessage({
          id: "accountingAudit.servicesProvided.description2",
        }),
      },
      {
        icon: "/imgs/accountingAudit/service3.png",
        title: intl.formatMessage({
          id: "accountingAudit.servicesProvided.title3",
        }),
        description: intl.formatMessage({
          id: "accountingAudit.servicesProvided.description3",
        }),
      },
    ],
    tax: [
      {
        icon: "/imgs/accountingAudit/service4.png",
        title: intl.formatMessage({
          id: "accountingAudit.servicesProvided.title4",
        }),
        description: intl.formatMessage({
          id: "accountingAudit.servicesProvided.description4",
        }),
      },
      {
        icon: "/imgs/accountingAudit/service5.png",
        title: intl.formatMessage({
          id: "accountingAudit.servicesProvided.title5",
        }),
        description: intl.formatMessage({
          id: "accountingAudit.servicesProvided.description5",
        }),
      },
      {
        icon: "/imgs/accountingAudit/service6.png",
        title: intl.formatMessage({
          id: "accountingAudit.servicesProvided.title6",
        }),
        description: intl.formatMessage({
          id: "accountingAudit.servicesProvided.description6",
        }),
      },
    ],
  };

  return (
    <section className="py-12 md:py-20 lg:py-24 xl:py-28">
      <div className="custom-container">
        <BadgeHeader
          badgeText={intl.formatMessage({
            id: "accountingAudit.servicesProvided.badge",
          })}
          badgeBgColor="bg-green-100"
          badgeTextColor="text-green-200"
          title={intl.formatMessage({
            id: "accountingAudit.servicesProvided.title",
          })}
          subTitle={intl.formatMessage({
            id: "accountingAudit.servicesProvided.subTitle",
          })}
        />

        {/* Audit Services Section */}
        <div className="mt-12 mb-16">
          <h2 className="text-xl md:text-2xl font-semibold text-center mb-8 relative">
            <span className="bg-background px-6">
              {intl.formatMessage({
                id: "accountingAudit.servicesProvided.auditServices",
              })}
            </span>
            <div className="absolute left-0 right-0 top-1/2 h-px bg-gray-200 -z-10"></div>
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
            {ServicesData.audit.map((item, index) => (
              <div
                key={index}
                className="bg-white rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 p-6 md:p-8 h-full flex flex-col"
              >
                {/* Service Icon */}
                <div className="flex justify-start mb-6">
                  <div className="relative w-50 h-50">
                    <Image
                      src={item.icon}
                      alt={item.title}
                      width={150}
                      height={150}
                      className="w-full h-full object-contain"
                    />
                  </div>
                </div>

                {/* Service Title */}
                <h3 className="font-semibold text-xl mb-3 text-start">
                  {item.title}
                </h3>

                {/* Service Description */}
                <p className="font-normal text-sm text-gray-700">
                  {item.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Tax Services Section */}
        <div>
          <h2 className="text-xl md:text-2xl font-semibold text-center mb-8 relative">
            <span className="bg-background px-6">
              {intl.formatMessage({
                id: "accountingAudit.servicesProvided.taxServices",
              })}
            </span>
            <div className="absolute left-0 right-0 top-1/2 h-px bg-gray-200 -z-10"></div>
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
            {ServicesData.tax.map((item, index) => (
              <div
                key={index}
                className="bg-white rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 p-6 md:p-8 h-full flex flex-col"
              >
                {/* Service Icon */}
                <div className="flex justify-start mb-6">
                  <div className="relative w-50 h-50">
                    <Image
                      src={item.icon}
                      alt={item.title}
                      width={150}
                      height={150}
                      className="w-full h-full object-contain"
                    />
                  </div>
                </div>

                {/* Service Title */}
                <h3 className="font-semibold text-xl mb-3 text-start">
                  {item.title}
                </h3>

                {/* Service Description */}
                <p className="font-normal text-sm text-gray-700">
                  {item.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default ServicesProvidedSection;
