import React from "react";
import Image from "next/image";
import { useIntl } from "react-intl";
import BadgeHeader from "../BadgeHeader";

interface HelpCardProps {
  title: string;
  description: string;
  imageSrc: string;
  imageAlt: string;
}

const HelpCard: React.FC<HelpCardProps> = ({
  title,
  description,
  imageSrc,
  imageAlt,
}) => {
  return (
    <div className="group cursor-pointer h-full flex flex-col">
      {/* Image Container */}
      <div className="relative w-full overflow-hidden rounded-xl md:rounded-2xl mb-6 md:mb-8 lg:mb-10">
        <Image
          src={imageSrc}
          alt={imageAlt}
          width={405}
          height={560}
          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
        />
        {/* Optional overlay for better text contrast if needed */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      </div>

      {/* Content */}
      <div className="flex flex-col items-center text-center space-y-3 md:space-y-4 flex-grow">
        <h3 className="text-lg sm:text-xl md:text-1xl lg:text-1xl xl:text-2xl font-semibold leading-tight">
          {title}
        </h3>
        <p className="font-normal text-sm sm:text-base md:text-lg leading-relaxed max-w-sm">
          {description}
        </p>
      </div>
    </div>
  );
};

export default function HelpSection() {
  const intl = useIntl();

  const helpCategories = [
    {
      title: intl.formatMessage({ id: "secretary.help.title1" }),
      description: intl.formatMessage({
        id: "secretary.help.description1",
      }),
      imageSrc: "/imgs/secretary/features-help1.png",
      imageAlt: intl.formatMessage({
        id: "secretary.help.imageAlt1",
      }),
    },
    {
      title: intl.formatMessage({ id: "secretary.help.title2" }),
      description: intl.formatMessage({
        id: "secretary.help.description2",
      }),
      imageSrc: "/imgs/secretary/features-help2.png",
      imageAlt: intl.formatMessage({
        id: "secretary.help.imageAlt2",
      }),
    },
    {
      title: intl.formatMessage({ id: "secretary.help.title3" }),
      description: intl.formatMessage({
        id: "secretary.help.description3",
      }),
      imageSrc: "/imgs/secretary/features-help3.png",
      imageAlt: intl.formatMessage({
        id: "secretary.help.imageAlt3",
      }),
    },
  ];

  return (
    <section className="py-12 md:py-20 lg:py-24 xl:py-28 bg-white">
      <div className="custom-container">
        <BadgeHeader
          badgeText={intl.formatMessage({
            id: "secretary.help.badge",
          })}
          badgeBgColor="bg-golden-100"
          badgeTextColor="text-golden-200"
          title={intl.formatMessage({ id: "secretary.help.title" })}
        />

        {/* Help Categories Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 lg:gap-10 mt-8 md:mt-12">
          {helpCategories.map((category, index) => (
            <HelpCard
              key={index}
              title={category.title}
              description={category.description}
              imageSrc={category.imageSrc}
              imageAlt={category.imageAlt}
            />
          ))}
        </div>
      </div>
    </section>
  );
}
