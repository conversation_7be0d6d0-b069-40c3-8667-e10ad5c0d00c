import React from "react";
import Image from "next/image";
import { useIntl } from "react-intl";
import { Button } from "../ui/button";

const HeroSection = () => {
  const intl = useIntl();

  return (
    <section className="custom-container py-8 md:py-16 lg:py-20">
      <div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="md:col-span-2">
            <div className="text-lg font-bold">
              {intl.formatMessage({ id: "home.hero.trustBadge" })}
            </div>

            <h1 className="text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-[900] md:mt-4 md:mb-9 my-2">
              {intl.formatMessage({ id: "home.hero.title" })}
            </h1>

            <Button className="font-bold md:rounded-[12px] rounded-sm !px-14">
              {intl.formatMessage({ id: "common.buttons.startFreeTrial" })}
            </Button>
            <div className="flex justify-end md:mt-0 mt-4">
              <Image
                src="/imgs/homeHeroSection2.png"
                alt="Business buildings"
                width={396}
                height={481}
                className="object-cover md:w-1/2 md:h-auto w-full md:rounded-2xl rounded-xl overflow-hidden"
              />
            </div>
          </div>

          <div className="md:col-span-1 md:space-y-8 md:mt-24 md:block flex gap-4">
            <Image
              src="/imgs/homeHeroSection1.png"
              alt="work"
              width={396}
              height={481}
              className="object-cover md:w-64 w-1/2 md:rounded-2xl rounded-xl overflow-hidden"
            />
            <Image
              src="/imgs/homeHeroSection3.png"
              alt="human"
              width={396}
              height={481}
              className="object-cover md:w-full w-1/2 md:rounded-2xl rounded-xl overflow-hidden"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
