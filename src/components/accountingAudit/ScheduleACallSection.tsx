import React from "react";
import { useIntl } from "react-intl";
import { Button } from "../ui/button";

export default function ScheduleACallSection() {
  const intl = useIntl();

  return (
    <section
      className={
        "bg-dark-100 text-white py-8 md:py-16 lg:py-20 bg-[url('/imgs/schedule-a-call.png')] bg-contain bg-no-repeat bg-[right_center]"
      }
    >
      <div className="custom-container">
        <div className="flex flex-col gap-8 lg:gap-12 xl:gap-16 lg:flex-row lg:items-center lg:justify-between">
          {/* Content Section */}
          <div className="flex-1 lg:max-w-[566px] xl:max-w-[600px]">
            <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-black leading-tight mb-4 md:mb-6 lg:mb-8">
              {intl.formatMessage({
                id: "accountingAudit.scheduleACall.title",
              })}
            </h1>

            <div className="text-sm md:text-base lg:text-lg leading-relaxed mb-8 md:mb-10 lg:mb-12 text-gray-200">
              {intl.formatMessage({
                id: "accountingAudit.scheduleACall.description",
              })}
            </div>

            {/* Buttons Section */}
            <div className="flex flex-col sm:flex-row gap-3 md:gap-4">
              <Button
                className="w-full sm:w-auto px-6 md:px-8 py-3 md:py-4 text-sm md:text-base font-medium"
                size="lg"
              >
                {intl.formatMessage({
                  id: "accountingAudit.scheduleACall.btnText",
                })}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
