import React from "react";
import GlobalContainer from "@/components/GlobalContainer";
import FeatureServicesSection from "@/components/bookkeeping/FeatureServicesSection";
import EvaluateSection from "@/components/bookkeeping/EvaluateSection";
import ServicesProvidedSection from "@/components/bookkeeping/ServicesProvidedSection";
import HelpSection from "@/components/bookkeeping/HelpSection";
import ChooseUsSection from "@/components/bookkeeping/ChooseUsSection";
import LaunchSection from "@/components/bookkeeping/ScheduleACallSection";
import StepsSection from "@/components/bookkeeping/StepsSection";
import Pricing from "@/components/bookkeeping/Pricing";
import FAQs from "@/components/bookkeeping/FAQs";
import PartnerNetworkSection from "@/components/bookkeeping/PartnerNetworkSection";

export default function Features() {
  return (
    <GlobalContainer headerDark={true}>
      <FeatureServicesSection />
      <EvaluateSection />
      <ServicesProvidedSection />
      <HelpSection />
      <ChooseUsSection />
      <LaunchSection />
      <StepsSection />
      <Pricing />
      <PartnerNetworkSection />
      <FAQs />
    </GlobalContainer>
  );
}
