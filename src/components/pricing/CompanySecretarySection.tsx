import React from "react";
import { useIntl } from "react-intl";
import { Button } from "@/components/ui/button";
import { Check } from "lucide-react";

interface ServiceItem {
  name: string;
  included: boolean;
}

export default function CompanySecretarySection() {
  const intl = useIntl();

  const serviceItems: ServiceItem[] = [
    {
      name: intl.formatMessage({ id: "pricing.companySecretary.service1" }),
      included: true,
    },
    {
      name: intl.formatMessage({ id: "pricing.companySecretary.service2" }),
      included: true,
    },
    {
      name: intl.formatMessage({ id: "pricing.companySecretary.service3" }),
      included: true,
    },
  ];

  return (
    <div className="w-full max-w-7xl mx-auto p-6 ">
      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        {/* Header Section */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 p-6 lg:p-8">
          {/* Left Side - Service Description */}
          <div className="lg:col-span-2 space-y-4">
            <h1 className="text-3xl lg:text-4xl font-bold text-gray-900">
              {intl.formatMessage({ id: "pricing.companySecretary.title" })}
            </h1>
            <p className="text-gray-600 text-base lg:text-lg">
              {intl.formatMessage({ id: "pricing.companySecretary.subtitle" })}
            </p>
          </div>

          {/* Right Side - Pricing Card */}
          <div className="lg:col-span-1 flex justify-center lg:justify-end">
            <div className="w-full max-w-sm">
              <div className="text-center pb-4">
                <div className="text-xl lg:text-2xl font-bold text-gray-900 mb-4">
                  {intl.formatMessage({
                    id: "pricing.companySecretary.basicPlan",
                  })}
                </div>
                <div className="space-y-2">
                  <p className="text-sm text-gray-600">
                    {intl.formatMessage({
                      id: "pricing.companySecretary.basicPrice",
                    })}
                  </p>
                  <p className="text-3xl lg:text-4xl font-bold text-gray-900">
                    {intl.formatMessage({
                      id: "pricing.companySecretary.price",
                    })}
                  </p>
                </div>
              </div>
              <div className="pt-0">
                <Button
                  className="w-full bg-blue-400 hover:bg-blue-500 text-white font-medium py-3 px-6 rounded-xl transition-colors duration-200"
                  variant="default"
                >
                  {intl.formatMessage({
                    id: "pricing.companySecretary.buttonText",
                  })}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Services Section */}
        <div className="px-6 lg:px-8 pb-6 lg:pb-8">
          <div className="space-y-6">
            <h2 className="text-xl lg:text-2xl font-semibold text-gray-900 border-b pb-3">
              {intl.formatMessage({
                id: "pricing.companySecretary.incorporationTitle",
              })}
            </h2>

            <div className="space-y-4">
              {serviceItems.map((service, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between py-4 border-b border-gray-100 last:border-b-0"
                >
                  <div className="flex-1">
                    <p className="text-base lg:text-lg font-medium text-gray-700">
                      {service.name}
                    </p>
                  </div>
                  <div className="flex justify-center items-center ml-4">
                    {service.included && (
                      <Check className="h-6 w-6 text-green-600" />
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
