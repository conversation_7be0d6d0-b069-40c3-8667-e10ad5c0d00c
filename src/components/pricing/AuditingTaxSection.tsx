import React from "react";
import { Button } from "@/components/ui/button";
import { Check } from "lucide-react";

interface PricingTier {
  turnover: string;
  fee: string;
}

export default function AuditingTaxSection() {
  const services = [
    "Preparation of Audited Financial Statements",
    "Ongoing expert support",
    "Profit Tax Computation",
    "Completed Profits Tax Return and filed to Inland Revenue Department",
  ];

  const pricingTiers: PricingTier[] = [
    { turnover: "No operations", fee: "3,500" },
    { turnover: "1 to 100,000", fee: "4,500" },
    { turnover: "100,000 - 500,000", fee: "5,000" },
    { turnover: "1m - 2m", fee: "5,500" },
    { turnover: "2m - 3m", fee: "6,000" },
    { turnover: "3m - 4m", fee: "7,000" },
    { turnover: "4m - 5m", fee: "8,000" },
    { turnover: "5m - 6m", fee: "9,500" },
    { turnover: "6m - 7m", fee: "11,000" },
    { turnover: "7m - 8m", fee: "12,500" },
    { turnover: "8m - 9m", fee: "14,500" },
    { turnover: "9m - 10m", fee: "16,500" },
    { turnover: "10m - 12m", fee: "18,500" },
    { turnover: "12m - 14m", fee: "20,500" },
    { turnover: "14m - 16m", fee: "22,250" },
    { turnover: "16m - 18m", fee: "24,500" },
    { turnover: "18m - 20m", fee: "26,500" },
    { turnover: "Above 20m", fee: "Independent quotation" },
  ];

  return (
    <div className="w-full max-w-7xl mx-auto p-6 ">
      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        {/* Header Section */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 p-6 bg-white border-b">
          {/* Left Column - Service Info */}
          <div className="lg:col-span-1 space-y-4">
            <h2 className="text-2xl font-bold text-gray-900">
              Auditing & Tax Service
            </h2>
            <p className="text-sm text-gray-600">
              The service charges are for reference only; actual fees will
              depend on individual circumstances.
            </p>

            {/* Services Included */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">
                Services included
              </h3>
              <div className="space-y-3">
                {services.map((service, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <Check className="h-5 w-5 mt-0.5 flex-shrink-0" />
                    <span className="text-sm">{service}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Column - Basic Plan Card */}
          <div className="lg:col-span-2 flex justify-center lg:justify-end">
            <div className="w-full max-w-sm text-center">
              <div className="pb-4">
                <div className="text-xl font-semibold text-gray-900">
                  Basic Plan
                </div>
              </div>
              <div className="pt-0">
                <Button className="w-full bg-blue-500 hover:bg-blue-600 text-white">
                  Get this plan
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Pricing Table */}
        <div className="p-6">
          <div className="overflow-x-auto">
            <div className="min-w-full">
              {/* Table Header */}
              <div className="grid grid-cols-2 gap-4 mb-6 pb-4 border-b-2 border-gray-200">
                <div className="text-center">
                  <h3 className="text-lg font-semibold text-gray-900">
                    Turnover (per year) (HKD)
                  </h3>
                </div>
                <div className="text-center">
                  <h3 className="text-lg font-semibold text-gray-900">
                    Fee(HKD)
                  </h3>
                </div>
              </div>

              {/* Table Rows */}
              <div className="space-y-3">
                {pricingTiers.map((tier, index) => (
                  <div
                    key={index}
                    className="grid grid-cols-2 gap-4 py-3 border-b border-gray-100 last:border-b-0 hover: transition-colors"
                  >
                    <div className="text-center">
                      <span className="text-sm text-gray-700">
                        {tier.turnover}
                      </span>
                    </div>
                    <div className="text-center">
                      <span className="text-sm font-medium text-gray-900">
                        {tier.fee === "Independent quotation"
                          ? tier.fee
                          : `${tier.fee}`}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
