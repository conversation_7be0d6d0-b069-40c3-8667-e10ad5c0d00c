import React from "react";
import { useIntl } from "react-intl";

export default function HeroSection() {
  const intl = useIntl();
  return (
    <section className={"py-8 md:py-16 lg:py-20"}>
      <div className="custom-container">
        <div className="flex flex-col gap-8 lg:gap-12 xl:gap-16 lg:flex-row lg:items-start lg:justify-between">
          {/* Content Section */}
          <div className="flex-1 lg:max-w-[566px] xl:max-w-[600px]">
            <div className="flex items-center font-normal text-sm md:text-base mb-4 md:mb-6">
              <span>
                {intl.formatMessage({
                  id: "pricing.hero.preTitle1",
                })}
              </span>
              <div className="w-1 h-1 bg-white rounded-full mx-2.5" />
              <span>
                {intl.formatMessage({
                  id: "pricing.hero.preTitle2",
                })}
              </span>
            </div>

            <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-black leading-tight mb-4 md:mb-6 lg:mb-8">
              {intl.formatMessage({ id: "pricing.hero.title" })}
            </h1>

            <div className="text-sm md:text-base lg:text-lg leading-relaxed mb-8 md:mb-10 lg:mb-12">
              {intl.formatMessage({
                id: "pricing.hero.description",
              })}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
