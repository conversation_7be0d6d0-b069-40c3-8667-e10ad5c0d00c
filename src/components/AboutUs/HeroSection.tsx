import React from "react";
import { useIntl } from "react-intl";

const HeroSection = () => {
  const intl = useIntl();

  return (
    <div className="relative h-screen">
      {/* Background Image */}
      <div className="w-full h-screen absolute inset-0">
        <img
          src="/imgs/aboutUs-bg.jpg"
          alt="About Us Background"
          className="w-full h-full object-cover"
        />
      </div>

      {/* Dark Overlay */}
      <div className="absolute inset-0 z-10 w-full h-screen bg-[#000000]/70"></div>

      {/* Content */}
      <div className="custom-container">
        <div className="absolute left-0 right-0 top-1/2 -translate-y-1/2 z-20 flex items-center justify-center text-center">
          <div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold text-white mb-6 md:mb-8">
              {intl.formatMessage({ id: "aboutUs.hero.title" })}
            </h1>
            <p className="text-lg md:text-xl lg:text-2xl text-white/90 leading-relaxed max-w-3xl">
              {intl.formatMessage({ id: "aboutUs.hero.description" })}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroSection;
