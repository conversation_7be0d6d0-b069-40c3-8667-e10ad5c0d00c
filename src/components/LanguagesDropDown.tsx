import React from "react";
import { useRouter } from "next/router";
import { Globe } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from "./ui/dropdown-menu";

import { But<PERSON> } from "./ui/button";
import { APP_LOCALES } from "@/constants/app";
import { getLocaleTitle } from "@/utils/locale";
import { cn } from "@/lib/utils";

export default function LanguagesDropDown({ dark = false }: { dark: boolean }) {
  const router = useRouter();

  const handleLocaleChange = (newLocale: string) => {
    router.push(router.pathname, router.asPath, { locale: newLocale });
  };
  return (
    <div
      className={cn(
        dark ? "bg-secondary/10" : "bg-secondary",
        "rounded-[12px]"
      )}
    >
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="plain"
            className={cn("px-2 py-2", dark ? "text-white" : "")}
          >
            <Globe className="w-4 h-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          align="end"
          className="p-2 rounded-2xl shadow-lg border-0 bg-white"
        >
          {APP_LOCALES.map((loc: string) => (
            <DropdownMenuItem
              key={loc}
              onClick={() => handleLocaleChange(loc)}
              className="px-4 py-3 text-base rounded-xl hover:bg-gray-50 cursor-pointer transition-colors"
            >
              {getLocaleTitle(loc)}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
