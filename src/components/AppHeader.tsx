import React, { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";

import NavigationBar from "./NavigationBar";
import LanguagesDropDown from "./LanguagesDropDown";
import { cn } from "@/lib/utils";
type AppHeaderProps = {
  dark?: boolean;
  isTransparent?: boolean;
  fixed?: boolean;
};

const AppHeader = ({
  dark = false,
  isTransparent = false,
  fixed = false,
}: AppHeaderProps) => {
  const [hasScrolledPastViewport, setHasScrolledPastViewport] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.scrollY;
      const viewportHeight = window.innerHeight;
      setHasScrolledPastViewport(scrollY > viewportHeight);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const getBackgroundClass = () => {
    if (hasScrolledPastViewport) {
      return "bg-background";
    }
    if (isTransparent) {
      return "bg-transparent";
    }
    if (dark) {
      return "bg-dark-100";
    }
    return "bg-background";
  };

  const isDarkMode = dark && !hasScrolledPastViewport;

  return (
    <div
      className={cn(
        "w-full h-16 md:h-24 top-0 z-50 transition-colors duration-300",
        fixed ? "fixed" : "sticky",
        getBackgroundClass()
      )}
    >
      <div className="custom-container w-full md:py-6 py-4">
        <div className="flex gap-6 items-center justify-between">
          <div className="flex justify-between items-center gap-4">
            <Link
              href="/"
              className="sm:w-60 sm:h-10 w-40 h-8 cursor-pointer object-cover"
            >
              <Image
                src={isDarkMode ? "/logo-white.png" : "/logo.png"}
                width={240}
                height={40}
                alt="logo"
                className="w-full h-full"
              />
            </Link>
            <LanguagesDropDown dark={isDarkMode} />
          </div>
          <NavigationBar dark={isDarkMode} />
        </div>
      </div>
    </div>
  );
};

export default AppHeader;
