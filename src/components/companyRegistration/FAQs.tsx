import React from "react";
import { useIntl } from "react-intl";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

export default function FAQs() {
  const intl = useIntl();

  const faqData = [
    {
      question: intl.formatMessage({ id: "companyRegistration.faq.question1" }),
      answer: intl.formatMessage({ id: "companyRegistration.faq.answer1" }),
    },
    {
      question: intl.formatMessage({ id: "companyRegistration.faq.question2" }),
      answer: intl.formatMessage({ id: "companyRegistration.faq.answer2" }),
    },
    {
      question: intl.formatMessage({ id: "companyRegistration.faq.question3" }),
      answer: intl.formatMessage({ id: "companyRegistration.faq.answer3" }),
    },
    {
      question: intl.formatMessage({ id: "companyRegistration.faq.question4" }),
      answer: intl.formatMessage({ id: "companyRegistration.faq.answer4" }),
    },
    {
      question: intl.formatMessage({ id: "companyRegistration.faq.question5" }),
      answer: intl.formatMessage({ id: "companyRegistration.faq.answer5" }),
    },
    {
      question: intl.formatMessage({ id: "companyRegistration.faq.question6" }),
      answer: intl.formatMessage({ id: "companyRegistration.faq.answer6" }),
    },
    {
      question: intl.formatMessage({ id: "companyRegistration.faq.question7" }),
      answer: intl.formatMessage({ id: "companyRegistration.faq.answer7" }),
    },
    {
      question: intl.formatMessage({ id: "companyRegistration.faq.question8" }),
      answer: intl.formatMessage({ id: "companyRegistration.faq.answer8" }),
    },
    {
      question: intl.formatMessage({ id: "companyRegistration.faq.question9" }),
      answer: intl.formatMessage({ id: "companyRegistration.faq.answer9" }),
    },
    {
      question: intl.formatMessage({
        id: "companyRegistration.faq.question10",
      }),
      answer: intl.formatMessage({ id: "companyRegistration.faq.answer10" }),
    },
  ];

  return (
    <section className="py-16 md:py-24">
      <div className="custom-container">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-start">
          {/* Left side - Title and subtitle */}
          <div>
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-4">
              {intl.formatMessage({ id: "companyRegistration.faq.title" })}
            </h2>
            <p className="text-base md:text-lg">
              {intl.formatMessage({ id: "companyRegistration.faq.subtitle" })}
            </p>
          </div>

          {/* Right side - FAQ Accordion */}
          <div>
            <Accordion type="single" collapsible className="w-full space-y-4">
              {faqData.map((faq, index) => (
                <AccordionItem
                  key={index}
                  value={`item-${index}`}
                  className="cursor-pointer bg-white rounded-lg border-0 px-6"
                >
                  <AccordionTrigger className="cursor-pointer text-left text-base md:text-lg font-medium hover:no-underline items-center">
                    {faq.question}
                  </AccordionTrigger>
                  <AccordionContent className="text-sm md:text-base leading-relaxed pb-6">
                    {faq.answer}
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>
        </div>
      </div>
    </section>
  );
}
