import React from "react";
import Image from "next/image";
import { useIntl } from "react-intl";
import { Button } from "../ui/button";

export default function StepsSection() {
  const intl = useIntl();

  const steps = [
    {
      title: intl.formatMessage({
        id: "companyRegistration.steps.step1.title",
      }),
      description: intl.formatMessage({
        id: "companyRegistration.steps.step1.description",
      }),
    },
    {
      title: intl.formatMessage({
        id: "companyRegistration.steps.step2.title",
      }),
      description: intl.formatMessage({
        id: "companyRegistration.steps.step2.description",
      }),
    },
    {
      title: intl.formatMessage({
        id: "companyRegistration.steps.step3.title",
      }),
      description: intl.formatMessage({
        id: "companyRegistration.steps.step3.description",
      }),
    },
  ];

  return (
    <section className="py-8 sm:py-12 md:py-16 lg:py-20 xl:py-24 bg-gray-50">
      <div className="custom-container">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 md:gap-10 lg:gap-12 xl:gap-16 items-start">
          {/* Image Section */}
          <div className="order-2 lg:order-1">
            <div className="relative w-full">
              <Image
                src="/imgs/steps.png"
                alt="Professional working on laptop"
                width={650}
                height={650}
                className="w-full h-auto object-cover rounded-xl md:rounded-2xl shadow-md md:shadow-lg"
                priority
              />
            </div>
          </div>

          {/* Content Section */}
          <div className="order-1 lg:order-2 space-y-4 sm:space-y-6 md:space-y-8">
            {/* Main Title */}
            <div className="space-y-3 sm:space-y-4 md:space-y-6">
              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-black leading-tight">
                {intl.formatMessage({ id: "companyRegistration.steps.title" })}
              </h2>

              <p className="text-sm sm:text-base md:text-lg lg:text-xl leading-relaxed">
                {intl.formatMessage({
                  id: "companyRegistration.steps.description",
                })}
              </p>
            </div>

            {/* Get Started Button */}
            <div className="pt-1 sm:pt-2">
              <Button
                className="w-full sm:w-auto bg-black hover:bg-gray-800 text-white px-4 sm:px-6 md:px-8 py-2.5 sm:py-3 md:py-4 text-sm md:text-base font-medium rounded-lg transition-colors"
                onClick={() => {}}
              >
                {intl.formatMessage({
                  id: "companyRegistration.steps.btnText",
                })}
              </Button>
            </div>

            {/* Steps List */}
            <div className="space-y-4 sm:space-y-6 md:space-y-8 pt-2 sm:pt-4 md:pt-6">
              {steps.map((step, index) => (
                <div key={index} className="">
                  <div className="flex-1 pt-0.5 sm:pt-1 flex items-center gap-5 mb-1 sm:mb-2 md:mb-3">
                    {/* Step Number Circle */}
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 bg-primary text-white rounded-full flex items-center justify-center font-bold text-sm sm:text-lg md:text-xl">
                        {index + 1}
                      </div>
                    </div>
                    <div className="text-base sm:text-lg md:text-xl lg:text-2xl font-bold">
                      {step.title}
                    </div>
                  </div>
                  <div className="flex gap-5">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 font-bold text-sm sm:text-lg md:text-xl">
                        {" "}
                      </div>
                    </div>
                    <div className="text-sm md:text-base lg:text-lg">
                      {step.description}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
