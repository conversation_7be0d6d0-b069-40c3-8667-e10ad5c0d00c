import Image from "next/image";
import React from "react";
import { useIntl } from "react-intl";

const partnerList = [
  {
    src: "/imgs/home/<USER>",
    alt: "auraCpa",
  },
  {
    src: "/imgs/home/<USER>",
    alt: "HSBC",
  },
  {
    src: "/imgs/home/<USER>",
    alt: "HangSeng",
  },
  {
    src: "/imgs/home/<USER>",
    alt: "bankOfChina",
  },
  {
    src: "/imgs/home/<USER>",
    alt: "standardChartered",
  },
  {
    src: "/imgs/home/<USER>",
    alt: "airWallex",
  },
];

const PartnerNetworkSection = () => {
  const intl = useIntl();

  return (
    <div className="custom-container">
      <div className="opacity-45 text-center mb-4">
        {intl.formatMessage({ id: "common.globalPartnerNetwork" })}
      </div>
      <div className="grid grid-cols-2 lg:grid-cols-3 gap-5">
        {partnerList.map((partner) => (
          <div key={partner.src}>
            <Image
              src={partner.src}
              alt={partner.alt}
              width={305}
              height={116}
              className="w-full h-full object-contain"
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default PartnerNetworkSection;
