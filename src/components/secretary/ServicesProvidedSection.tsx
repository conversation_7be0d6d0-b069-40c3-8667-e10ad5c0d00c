import React from "react";
import { useIntl } from "react-intl";
import Image from "next/image";
import BadgeHeader from "../BadgeHeader";

const ServicesProvidedSection = () => {
  const intl = useIntl();

  const ServicesData = [
    {
      icon: "/imgs/secretary/service1.png",
      title: intl.formatMessage({
        id: "secretary.servicesProvided.title1",
      }),
      description: intl.formatMessage({
        id: "secretary.servicesProvided.description1",
      }),
    },
    {
      icon: "/imgs/secretary/service2.png",
      title: intl.formatMessage({
        id: "secretary.servicesProvided.title2",
      }),
      description: intl.formatMessage({
        id: "secretary.servicesProvided.description2",
      }),
    },
    {
      icon: "/imgs/secretary/service3.png",
      title: intl.formatMessage({
        id: "secretary.servicesProvided.title3",
      }),
      description: intl.formatMessage({
        id: "secretary.servicesProvided.description3",
      }),
    },
    {
      icon: "/imgs/secretary/service4.png",
      title: intl.formatMessage({
        id: "secretary.servicesProvided.title4",
      }),
      description: intl.formatMessage({
        id: "secretary.servicesProvided.description4",
      }),
    },
    {
      icon: "/imgs/secretary/service5.png",
      title: intl.formatMessage({
        id: "secretary.servicesProvided.title5",
      }),
      description: intl.formatMessage({
        id: "secretary.servicesProvided.description5",
      }),
    },
  ];

  return (
    <section className="py-12 md:py-20 lg:py-24 xl:py-28">
      <div className="custom-container">
        <BadgeHeader
          badgeText={intl.formatMessage({
            id: "secretary.servicesProvided.badge",
          })}
          badgeBgColor="bg-green-100"
          badgeTextColor="text-green-200"
          title={intl.formatMessage({
            id: "secretary.servicesProvided.title",
          })}
          subTitle={intl.formatMessage({
            id: "secretary.servicesProvided.subTitle",
          })}
        />

        {/* 调整 1: 将 Grid 布局改为 Flexbox 布局 */}
        <div className="flex flex-wrap justify-center gap-4 md:gap-6 lg:gap-8 mt-8 md:mt-12">
          {ServicesData?.map((item, index) => (
            <div
              key={index}
              // 调整 2: 为卡片设置响应式宽度
              className="w-full sm:w-[48%] lg:w-[31%] bg-white rounded-xl md:rounded-2xl hover:shadow-xl transition-all duration-300 p-4 md:p-6 lg:p-8 h-full flex flex-col items-start group hover:-translate-y-1"
            >
              <div className="flex justify-center w-full mb-4 md:mb-6">
                <div className="relative w-50 h-50">
                  <Image
                    src={item.icon}
                    alt={item.title}
                    width={150}
                    height={150}
                    className="w-full h-full object-contain transition-transform duration-300 group-hover:scale-105"
                  />
                </div>
              </div>

              <h3 className="font-semibold text-lg sm:text-xl md:text-xl xl:text-2xl mb-3 md:mb-4 w-full">
                {item.title}
              </h3>

              <p className="font-normal flex-grow w-full">{item.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ServicesProvidedSection;
