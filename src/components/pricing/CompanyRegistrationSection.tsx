import React from "react";
import { useIntl } from "react-intl";
import { Button } from "@/components/ui/button";
import { Check, X } from "lucide-react";

interface ServiceItem {
  name: string;
  greenBox: boolean | string;
  essential: boolean | string;
  comprehensive: boolean | string;
}

interface PricingPlan {
  name: string;
  price: string;
  buttonText: string;
  buttonVariant: "default" | "secondary" | "outline";
}

export default function CompanyRegistrationSection() {
  const intl = useIntl();

  const plans: PricingPlan[] = [
    {
      name: intl.formatMessage({
        id: "pricing.companyRegistration.greenBoxPackage",
      }),
      price: intl.formatMessage({
        id: "pricing.companyRegistration.greenBoxPrice",
      }),
      buttonText: intl.formatMessage({
        id: "pricing.companyRegistration.buttonText",
      }),
      buttonVariant: "default",
    },
    {
      name: intl.formatMessage({
        id: "pricing.companyRegistration.essentialPackage",
      }),
      price: intl.formatMessage({
        id: "pricing.companyRegistration.essentialPrice",
      }),
      buttonText: intl.formatMessage({
        id: "pricing.companyRegistration.buttonText",
      }),
      buttonVariant: "default",
    },
    {
      name: intl.formatMessage({
        id: "pricing.companyRegistration.comprehensivePackage",
      }),
      price: intl.formatMessage({
        id: "pricing.companyRegistration.comprehensivePrice",
      }),
      buttonText: intl.formatMessage({
        id: "pricing.companyRegistration.buttonText",
      }),
      buttonVariant: "default",
    },
  ];

  const includedServices: ServiceItem[] = [
    {
      name: intl.formatMessage({
        id: "pricing.companyRegistration.companyRegistryFee1",
      }),
      greenBox: intl.formatMessage({
        id: "pricing.companyRegistration.registryFee1",
      }),
      essential: intl.formatMessage({
        id: "pricing.companyRegistration.registryFee1",
      }),
      comprehensive: intl.formatMessage({
        id: "pricing.companyRegistration.registryFee1",
      }),
    },
    {
      name: intl.formatMessage({
        id: "pricing.companyRegistration.companyRegistryFee2",
      }),
      greenBox: intl.formatMessage({
        id: "pricing.companyRegistration.registryFee2",
      }),
      essential: intl.formatMessage({
        id: "pricing.companyRegistration.registryFee2",
      }),
      comprehensive: intl.formatMessage({
        id: "pricing.companyRegistration.registryFee2",
      }),
    },
  ];

  const incorporationServices: ServiceItem[] = [
    {
      name: intl.formatMessage({
        id: "pricing.companyRegistration.companyNameSearch",
      }),
      greenBox: true,
      essential: true,
      comprehensive: true,
    },
    {
      name: intl.formatMessage({
        id: "pricing.companyRegistration.businessRegistration",
      }),
      greenBox: true,
      essential: true,
      comprehensive: true,
    },
    {
      name: intl.formatMessage({
        id: "pricing.companyRegistration.certificateIncorporation",
      }),
      greenBox: true,
      essential: true,
      comprehensive: true,
    },
    {
      name: intl.formatMessage({
        id: "pricing.companyRegistration.articlesAssociation",
      }),
      greenBox: intl.formatMessage({
        id: "pricing.companyRegistration.fiveCopies",
      }),
      essential: intl.formatMessage({
        id: "pricing.companyRegistration.fiveCopies",
      }),
      comprehensive: intl.formatMessage({
        id: "pricing.companyRegistration.fiveCopies",
      }),
    },
    {
      name: intl.formatMessage({
        id: "pricing.companyRegistration.companyRegister",
      }),
      greenBox: true,
      essential: true,
      comprehensive: true,
    },
    {
      name: intl.formatMessage({
        id: "pricing.companyRegistration.significantControllers",
      }),
      greenBox: true,
      essential: true,
      comprehensive: true,
    },
    {
      name: intl.formatMessage({
        id: "pricing.companyRegistration.firstBoardMeeting",
      }),
      greenBox: true,
      essential: true,
      comprehensive: true,
    },
    {
      name: intl.formatMessage({
        id: "pricing.companyRegistration.shareRegister",
      }),
      greenBox: true,
      essential: true,
      comprehensive: true,
    },
    {
      name: intl.formatMessage({
        id: "pricing.companyRegistration.companySeals",
      }),
      greenBox: true,
      essential: true,
      comprehensive: true,
    },
    {
      name: intl.formatMessage({
        id: "pricing.companyRegistration.requiredTime",
      }),
      greenBox: true,
      essential: true,
      comprehensive: true,
    },
  ];

  const secretaryServices: ServiceItem[] = [
    {
      name: intl.formatMessage({
        id: "pricing.companyRegistration.agmDocuments",
      }),
      greenBox: false,
      essential: true,
      comprehensive: true,
    },
    {
      name: intl.formatMessage({
        id: "pricing.companyRegistration.annualReturns",
      }),
      greenBox: false,
      essential: true,
      comprehensive: true,
    },
  ];

  const registeredAddressServices: ServiceItem[] = [
    {
      name: intl.formatMessage({
        id: "pricing.companyRegistration.mailNotifications",
      }),
      greenBox: false,
      essential: false,
      comprehensive: true,
    },
    {
      name: intl.formatMessage({
        id: "pricing.companyRegistration.scanningForwarding",
      }),
      greenBox: false,
      essential: false,
      comprehensive: true,
    },
  ];

  const optionalServices: ServiceItem[] = [
    {
      name: intl.formatMessage({
        id: "pricing.companyRegistration.bankAccountAssistance",
      }),
      greenBox: intl.formatMessage({
        id: "pricing.companyRegistration.bankAccountFee",
      }),
      essential: intl.formatMessage({
        id: "pricing.companyRegistration.bankAccountFee",
      }),
      comprehensive: intl.formatMessage({
        id: "pricing.companyRegistration.bankAccountFee",
      }),
    },
  ];

  const renderServiceValue = (value: boolean | string) => {
    if (typeof value === "boolean") {
      return value ? (
        <Check className="h-5 w-5 mx-auto" />
      ) : (
        <X className="h-5 w-5 mx-auto" />
      );
    }
    return <span className="text-sm">{value}</span>;
  };

  const ServiceSection = ({
    title,
    services,
    className = "",
  }: {
    title: string;
    services: ServiceItem[];
    className?: string;
  }) => (
    <div className={`space-y-4 ${className}`}>
      <h3 className="text-lg font-semibold pb-2">{title}</h3>
      {services.map((service, index) => (
        <div
          key={index}
          className="grid grid-cols-1 md:grid-cols-4 gap-4 py-3 border-b border-[#D9D9D9]"
        >
          <div className="md:col-span-1">
            <p className="text-sm font-medium">{service.name}</p>
          </div>
          <div className="grid grid-cols-3 md:contents gap-4">
            <div className="flex justify-center items-center">
              {renderServiceValue(service.greenBox)}
            </div>
            <div className="flex justify-center items-center">
              {renderServiceValue(service.essential)}
            </div>
            <div className="flex justify-center items-center">
              {renderServiceValue(service.comprehensive)}
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <div className="w-full max-w-7xl mx-auto p-6 ">
      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        {/* Header Section */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 p-6 bg-white">
          <div className="md:col-span-1">
            <div className="space-y-4">
              <h2 className="text-2xl font-bold">
                {intl.formatMessage({
                  id: "pricing.companyRegistration.title",
                })}
              </h2>
              <p className="text-sm ">
                {intl.formatMessage({
                  id: "pricing.companyRegistration.subtitle",
                })}
              </p>
            </div>
          </div>

          {/* Pricing Cards */}
          <div className="md:col-span-3 grid grid-cols-1 md:grid-cols-3 gap-4">
            {plans.map((plan, index) => (
              <div key={index} className="text-center border-0">
                <div className="pb-4">
                  <div className="text-2xl font-semibold">{plan.name}</div>
                  <div className="space-y-2">
                    <p className="text-sm font-medium ">
                      {intl.formatMessage({
                        id: "pricing.companyRegistration.basicPrice",
                      })}
                    </p>
                    <p className="text-3xl font-bold">{plan.price}</p>
                  </div>
                </div>
                <div className="pt-0">
                  <Button
                    className="w-full bg-primary"
                    variant={plan.buttonVariant}
                  >
                    {plan.buttonText}
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Services Comparison */}
        <div className="p-6 space-y-8">
          {/* Mobile Headers for Service Columns */}
          <div className="md:hidden grid grid-cols-3 gap-4 mb-6 text-center">
            <div className="font-semibold text-sm">
              {intl.formatMessage({
                id: "pricing.companyRegistration.greenBoxHeader",
              })}
            </div>
            <div className="font-semibold text-sm">
              {intl.formatMessage({
                id: "pricing.companyRegistration.essentialHeader",
              })}
            </div>
            <div className="font-semibold text-sm">
              {intl.formatMessage({
                id: "pricing.companyRegistration.comprehensiveHeader",
              })}
            </div>
          </div>

          <ServiceSection
            title={intl.formatMessage({
              id: "pricing.companyRegistration.includedTitle",
            })}
            services={includedServices}
          />
          <ServiceSection
            title={intl.formatMessage({
              id: "pricing.companyRegistration.incorporationTitle",
            })}
            services={incorporationServices}
          />
          <ServiceSection
            title={intl.formatMessage({
              id: "pricing.companyRegistration.secretaryServicesTitle",
            })}
            services={secretaryServices}
          />
          <ServiceSection
            title={intl.formatMessage({
              id: "pricing.companyRegistration.registeredAddressTitle",
            })}
            services={registeredAddressServices}
          />
          <ServiceSection
            title={intl.formatMessage({
              id: "pricing.companyRegistration.optionalTitle",
            })}
            services={optionalServices}
          />
        </div>
      </div>
    </div>
  );
}
