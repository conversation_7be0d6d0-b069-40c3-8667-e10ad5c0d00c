import Image from "next/image";
import React from "react";
import { useIntl } from "react-intl";

const partnerList = [
  {
    src: "/imgs/services/CPA1.png",
    alt: "Hong Kong Certified Public Accountants",
    text: "common.certification.hkCPA1",
  },
  {
    src: "/imgs/services/CPA2.png",
    alt: "Hong Kong Certified Public Accountants",
    text: "common.certification.hkCPA2",
  },
  {
    src: "/imgs/services/CR.png",
    alt: "TC010022",
    text: "common.certification.tcNumber",
  },
];

const PartnerNetworkSection = () => {
  const intl = useIntl();
  return (
    <div className="bg-white">
      <div className="custom-container py-8 md:py-10 lg:py-12">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-5">
          {partnerList.map((partner) => (
            <div key={partner.src}>
              <Image
                src={partner.src}
                alt={partner.alt}
                width={305}
                height={116}
                className="w-full h-full object-contain"
              />
              <div className="text-center text-sm text-[#0000007A]">
                {intl.formatMessage({ id: partner.text })}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PartnerNetworkSection;
