import React from "react";
import { useIntl } from "react-intl";
import Image from "next/image";

import BadgeHeader from "../BadgeHeader";

const ChooseUsSection = () => {
  const intl = useIntl();
  return (
    <div className="custom-container">
      <BadgeHeader
        badgeText={intl.formatMessage({ id: "home.whyChooseUs.badge" })}
        badgeBgColor="bg-green-100"
        badgeTextColor="text-green-200"
        title={intl.formatMessage({ id: "home.whyChooseUs.title" })}
      />
      {/* content */}
      <div className="grid lg:grid-cols-3 md:grid-cols-2 grid-cols-1 lg:gap-5 gap-3">
        {/* full suite */}
        <div className="flex-1 bg-white p-6 rounded-2xl pt-9">
          <div className="lg:text-4xl text-2xl font-extrabold">
            <div>
              {intl.formatMessage({ id: "home.whyChooseUs.fullSuite.title" })}
            </div>
            <div>
              {intl.formatMessage({
                id: "home.whyChooseUs.fullSuite.solution",
              })}
            </div>
          </div>
          <div className="mt-2 mb-9">
            {intl.formatMessage({
              id: "home.whyChooseUs.fullSuite.description",
            })}
          </div>
          <div className="space-y-4">
            {[
              intl.formatMessage({
                id: "home.whyChooseUs.fullSuite.companyRegistration",
              }),
              intl.formatMessage({
                id: "home.whyChooseUs.fullSuite.accounting",
              }),
              intl.formatMessage({
                id: "home.whyChooseUs.fullSuite.compliance",
              }),
            ].map((item) => (
              <div key={item} className="flex items-center gap-2">
                <div className="w-5 h-5">
                  <Image
                    src="/imgs/tick-circle-solid.png"
                    width={24}
                    height={24}
                    alt="Tick"
                    className="w-full h-full"
                  />
                </div>
                <div>{item}</div>
              </div>
            ))}
          </div>
        </div>
        {/* 7/24 */}
        <div className="flex-1 flex flex-col lg:gap-5 gap-3">
          <div className="flex-1 bg-white p-6 rounded-2xl flex flex-col justify-center items-center lg:gap-5 gap-3">
            <div className="lg:text-4xl text-2xl font-extrabold">
              {intl.formatMessage({ id: "home.whyChooseUs.support247.title" })}
            </div>
            <div>
              {intl.formatMessage({
                id: "home.whyChooseUs.support247.description",
              })}
            </div>
          </div>
          <div className="flex-1 bg-white  p-6 rounded-2xl flex flex-col justify-center items-center lg:gap-5 gap-3">
            <div className="lg:text-4xl text-2xl font-extrabold">
              {intl.formatMessage({ id: "home.whyChooseUs.experience.title" })}
            </div>
            <div className="text-center">
              {intl.formatMessage({
                id: "home.whyChooseUs.experience.description",
              })}
            </div>
          </div>
        </div>
        {/* no hidden fees */}
        <div className="flex-1 bg-white p-6 rounded-2xl">
          <div className="sm:w-1/2 md:w-full w-full">
            <Image
              src="/imgs/NoHiddenFees.png"
              alt="no-hidden-fees"
              width={828}
              height={432}
              className="w-full h-full object-cover"
            />
          </div>
          <div className="lg:text-4xl text-2xl font-extrabold lg:my-9 my-4">
            {intl.formatMessage({ id: "home.whyChooseUs.noHiddenFees.title" })}
          </div>
          <div>
            {intl.formatMessage({
              id: "home.whyChooseUs.noHiddenFees.description",
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChooseUsSection;
