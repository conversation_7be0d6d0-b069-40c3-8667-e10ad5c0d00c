import React from "react";
import { useIntl } from "react-intl";
import { Button } from "@/components/ui/button";
import { Check } from "lucide-react";

interface PricingTier {
  turnover: string;
  fee: string;
}

export default function AuditingTaxSection() {
  const intl = useIntl();

  const services = [
    intl.formatMessage({ id: "pricing.auditingTax.service1" }),
    intl.formatMessage({ id: "pricing.auditingTax.service2" }),
    intl.formatMessage({ id: "pricing.auditingTax.service3" }),
    intl.formatMessage({ id: "pricing.auditingTax.service4" }),
  ];

  const pricingTiers: PricingTier[] = [
    {
      turnover: intl.formatMessage({ id: "pricing.auditingTax.noOperations" }),
      fee: "3,500",
    },
    {
      turnover: intl.formatMessage({ id: "pricing.auditingTax.range1to100k" }),
      fee: "4,500",
    },
    {
      turnover: intl.formatMessage({
        id: "pricing.auditingTax.range100kto500k",
      }),
      fee: "5,000",
    },
    {
      turnover: intl.formatMessage({ id: "pricing.auditingTax.range1mto2m" }),
      fee: "5,500",
    },
    {
      turnover: intl.formatMessage({ id: "pricing.auditingTax.range2mto3m" }),
      fee: "6,000",
    },
    {
      turnover: intl.formatMessage({ id: "pricing.auditingTax.range3mto4m" }),
      fee: "7,000",
    },
    {
      turnover: intl.formatMessage({ id: "pricing.auditingTax.range4mto5m" }),
      fee: "8,000",
    },
    {
      turnover: intl.formatMessage({ id: "pricing.auditingTax.range5mto6m" }),
      fee: "9,500",
    },
    {
      turnover: intl.formatMessage({ id: "pricing.auditingTax.range6mto7m" }),
      fee: "11,000",
    },
    {
      turnover: intl.formatMessage({ id: "pricing.auditingTax.range7mto8m" }),
      fee: "12,500",
    },
    {
      turnover: intl.formatMessage({ id: "pricing.auditingTax.range8mto9m" }),
      fee: "14,500",
    },
    {
      turnover: intl.formatMessage({ id: "pricing.auditingTax.range9mto10m" }),
      fee: "16,500",
    },
    {
      turnover: intl.formatMessage({ id: "pricing.auditingTax.range10mto12m" }),
      fee: "18,500",
    },
    {
      turnover: intl.formatMessage({ id: "pricing.auditingTax.range12mto14m" }),
      fee: "20,500",
    },
    {
      turnover: intl.formatMessage({ id: "pricing.auditingTax.range14mto16m" }),
      fee: "22,250",
    },
    {
      turnover: intl.formatMessage({ id: "pricing.auditingTax.range16mto18m" }),
      fee: "24,500",
    },
    {
      turnover: intl.formatMessage({ id: "pricing.auditingTax.range18mto20m" }),
      fee: "26,500",
    },
    {
      turnover: intl.formatMessage({ id: "pricing.auditingTax.above20m" }),
      fee: intl.formatMessage({
        id: "pricing.auditingTax.independentQuotation",
      }),
    },
  ];

  return (
    <div className="w-full max-w-7xl mx-auto p-6 ">
      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        {/* Header Section */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 p-6 bg-white border-b">
          {/* Left Column - Service Info */}
          <div className="lg:col-span-1 space-y-4">
            <h2 className="text-2xl font-bold text-gray-900">
              {intl.formatMessage({ id: "pricing.auditingTax.title" })}
            </h2>
            <p className="text-sm text-gray-600">
              {intl.formatMessage({ id: "pricing.auditingTax.subtitle" })}
            </p>

            {/* Services Included */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">
                {intl.formatMessage({
                  id: "pricing.auditingTax.servicesIncluded",
                })}
              </h3>
              <div className="space-y-3">
                {services.map((service, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <Check className="h-5 w-5 mt-0.5 flex-shrink-0" />
                    <span className="text-sm">{service}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Column - Basic Plan Card */}
          <div className="lg:col-span-2 flex justify-center lg:justify-end">
            <div className="w-full max-w-sm text-center">
              <div className="pb-4">
                <div className="text-xl font-semibold text-gray-900">
                  {intl.formatMessage({ id: "pricing.auditingTax.basicPlan" })}
                </div>
              </div>
              <div className="pt-0">
                <Button className="w-full bg-blue-500 hover:bg-blue-600 text-white">
                  {intl.formatMessage({ id: "pricing.auditingTax.buttonText" })}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Pricing Table */}
        <div className="p-6">
          <div className="overflow-x-auto">
            <div className="min-w-full">
              {/* Table Header */}
              <div className="grid grid-cols-2 gap-4 mb-6 pb-4 border-b-2 border-gray-200">
                <div className="text-center">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {intl.formatMessage({
                      id: "pricing.auditingTax.turnoverHeader",
                    })}
                  </h3>
                </div>
                <div className="text-center">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {intl.formatMessage({
                      id: "pricing.auditingTax.feeHeader",
                    })}
                  </h3>
                </div>
              </div>

              {/* Table Rows */}
              <div className="space-y-3">
                {pricingTiers.map((tier, index) => (
                  <div
                    key={index}
                    className="grid grid-cols-2 gap-4 py-3 border-b border-gray-100 last:border-b-0 hover: transition-colors"
                  >
                    <div className="text-center">
                      <span className="text-sm text-gray-700">
                        {tier.turnover}
                      </span>
                    </div>
                    <div className="text-center">
                      <span className="text-sm font-medium text-gray-900">
                        {tier.fee ===
                        intl.formatMessage({
                          id: "pricing.auditingTax.independentQuotation",
                        })
                          ? tier.fee
                          : `${tier.fee}`}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
