import React from "react";
import { useIntl } from "react-intl";

interface StatItemProps {
  value: string;
  label: string;
}

const StatItem: React.FC<StatItemProps> = ({ value, label }) => {
  return (
    <div className="text-center text-white">
      <div className="text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold mb-2 md:mb-4">
        {value}
      </div>
      <div className="text-sm md:text-base lg:text-lg text-white/80 font-medium">
        {label}
      </div>
    </div>
  );
};

const StatsSection = () => {
  const intl = useIntl();

  const stats = [
    {
      value: intl.formatMessage({ id: "aboutUs.stats.clientsRecommend" }),
      label: intl.formatMessage({ id: "aboutUs.stats.clientsRecommendLabel" }),
    },
    {
      value: intl.formatMessage({ id: "aboutUs.stats.yearsExperience" }),
      label: intl.formatMessage({ id: "aboutUs.stats.yearsExperienceLabel" }),
    },
    {
      value: intl.formatMessage({ id: "aboutUs.stats.successfulCases" }),
      label: intl.formatMessage({ id: "aboutUs.stats.successfulCasesLabel" }),
    },
    {
      value: intl.formatMessage({ id: "aboutUs.stats.responseTime" }),
      label: intl.formatMessage({ id: "aboutUs.stats.responseTimeLabel" }),
    },
  ];

  return (
    <section className="relative py-16 md:py-24 overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0">
        <img
          src="/imgs/city-background.jpg"
          alt="City Background"
          className="w-full h-full object-cover"
        />
      </div>

      {/* Dark Overlay */}
      <div className="absolute inset-0 bg-black/70"></div>

      {/* Content */}
      <div className="relative z-10 custom-container">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-10 md:gap-12 lg:gap-16">
          {stats.map((stat, index) => (
            <StatItem key={index} value={stat.value} label={stat.label} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default StatsSection;
